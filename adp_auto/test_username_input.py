#!/usr/bin/env python3
"""
Focused test script for ADP username input and Next button
This script specifically tests the username input interaction to enable the Next button
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    debug_dir = os.path.join(os.getcwd(), "username_test_debug")
    os.makedirs(debug_dir, exist_ok=True)
    
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 30)
    
    return driver, wait, debug_dir

def take_screenshot(driver, debug_dir, filename):
    """Take a screenshot"""
    try:
        filepath = os.path.join(debug_dir, f"{filename}.png")
        driver.save_screenshot(filepath)
        print(f"📸 Screenshot: {filepath}")
    except Exception as e:
        print(f"❌ Screenshot failed: {e}")

def test_username_input_methods(driver, debug_dir, username):
    """Test different methods to input username and enable Next button"""
    print("\n🔍 Testing Username Input Methods")
    print("=" * 50)
    
    take_screenshot(driver, debug_dir, "01_initial_page")
    
    # Method 1: Enhanced JavaScript approach
    print("\n📝 Method 1: Enhanced JavaScript")
    try:
        js_result = driver.execute_script(f"""
        var sdfInput = document.getElementById('login-form_username');
        if (!sdfInput) return 'no_input_found';
        
        console.log('Found sdf-input element');
        
        // Set value directly
        sdfInput.value = '{username}';
        
        // Find internal input
        var internalInput = sdfInput.querySelector('input');
        if (internalInput) {{
            console.log('Found internal input element');
            internalInput.value = '{username}';
            
            // Trigger comprehensive events
            internalInput.dispatchEvent(new Event('focus', {{ bubbles: true }}));
            internalInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
            internalInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
            internalInput.dispatchEvent(new Event('keyup', {{ bubbles: true }}));
            internalInput.dispatchEvent(new Event('blur', {{ bubbles: true }}));
        }}
        
        // Trigger events on sdf-input
        sdfInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
        sdfInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
        
        // Check Next button status
        var nextBtn = document.getElementById('verifUseridBtn');
        var nextBtnStatus = nextBtn ? {{
            disabled: nextBtn.disabled,
            ariaDisabled: nextBtn.getAttribute('aria-disabled'),
            classList: nextBtn.className
        }} : 'not_found';
        
        return {{
            method: 'javascript',
            inputValue: internalInput ? internalInput.value : sdfInput.value,
            nextButtonStatus: nextBtnStatus
        }};
        """)
        
        print(f"✅ JavaScript result: {js_result}")
        take_screenshot(driver, debug_dir, "02_after_javascript")
        
        if js_result.get('nextButtonStatus', {}).get('disabled') == False:
            print("🎉 Next button enabled via JavaScript!")
            return True
            
    except Exception as e:
        print(f"❌ JavaScript method failed: {e}")
    
    # Method 2: Direct interaction
    print("\n🖱️ Method 2: Direct Interaction")
    try:
        sdf_input = driver.find_element(By.ID, "login-form_username")
        print("✅ Found sdf-input element")
        
        # Click to focus
        sdf_input.click()
        time.sleep(1)
        
        # Try to find internal input
        try:
            internal_input = sdf_input.find_element(By.TAG_NAME, "input")
            print("✅ Found internal input element")
            internal_input.clear()
            internal_input.send_keys(username)
            print(f"✅ Entered username: {username}")
        except:
            print("⚠️ No internal input found, using sdf-input directly")
            sdf_input.clear()
            sdf_input.send_keys(username)
        
        take_screenshot(driver, debug_dir, "03_after_direct_input")
        
        # Check Next button
        time.sleep(2)
        next_btn = driver.find_element(By.ID, "verifUseridBtn")
        is_disabled = next_btn.get_attribute("disabled")
        
        if not is_disabled:
            print("🎉 Next button enabled via direct interaction!")
            return True
        else:
            print("⚠️ Next button still disabled after direct interaction")
            
    except Exception as e:
        print(f"❌ Direct interaction failed: {e}")
    
    # Method 3: Selenium Actions
    print("\n⌨️ Method 3: Selenium Actions")
    try:
        from selenium.webdriver.common.action_chains import ActionChains
        
        sdf_input = driver.find_element(By.ID, "login-form_username")
        
        # Use ActionChains for more realistic interaction
        actions = ActionChains(driver)
        actions.click(sdf_input)
        actions.pause(0.5)
        
        # Try to find internal input for actions
        try:
            internal_input = sdf_input.find_element(By.TAG_NAME, "input")
            actions.click(internal_input)
            actions.pause(0.5)
            actions.send_keys(username)
        except:
            actions.send_keys(username)
        
        actions.perform()
        print(f"✅ Entered username via Actions: {username}")
        
        take_screenshot(driver, debug_dir, "04_after_actions")
        
        # Check Next button
        time.sleep(2)
        next_btn = driver.find_element(By.ID, "verifUseridBtn")
        is_disabled = next_btn.get_attribute("disabled")
        
        if not is_disabled:
            print("🎉 Next button enabled via Actions!")
            return True
        else:
            print("⚠️ Next button still disabled after Actions")
            
    except Exception as e:
        print(f"❌ Actions method failed: {e}")
    
    # Method 4: Character-by-character input
    print("\n🔤 Method 4: Character-by-character Input")
    try:
        sdf_input = driver.find_element(By.ID, "login-form_username")
        sdf_input.click()
        time.sleep(1)
        
        # Clear first
        try:
            internal_input = sdf_input.find_element(By.TAG_NAME, "input")
            internal_input.clear()
            target_element = internal_input
        except:
            sdf_input.clear()
            target_element = sdf_input
        
        # Send characters one by one
        for char in username:
            target_element.send_keys(char)
            time.sleep(0.1)  # Small delay between characters
        
        print(f"✅ Entered username character by character: {username}")
        
        take_screenshot(driver, debug_dir, "05_after_char_by_char")
        
        # Check Next button
        time.sleep(2)
        next_btn = driver.find_element(By.ID, "verifUseridBtn")
        is_disabled = next_btn.get_attribute("disabled")
        
        if not is_disabled:
            print("🎉 Next button enabled via character-by-character!")
            return True
        else:
            print("⚠️ Next button still disabled after character-by-character")
            
    except Exception as e:
        print(f"❌ Character-by-character method failed: {e}")
    
    return False

def analyze_page_state(driver, debug_dir):
    """Analyze the current page state"""
    print("\n🔍 Analyzing Page State")
    print("=" * 30)
    
    try:
        # Get username field state
        username_state = driver.execute_script("""
        var sdfInput = document.getElementById('login-form_username');
        if (!sdfInput) return 'not_found';
        
        var internalInput = sdfInput.querySelector('input');
        return {
            sdfInputValue: sdfInput.value || '',
            internalInputValue: internalInput ? internalInput.value : 'no_internal_input',
            sdfInputHTML: sdfInput.outerHTML.substring(0, 200) + '...',
            hasInternalInput: !!internalInput
        };
        """)
        
        print(f"📝 Username field state: {username_state}")
        
        # Get Next button state
        next_btn_state = driver.execute_script("""
        var nextBtn = document.getElementById('verifUseridBtn');
        if (!nextBtn) return 'not_found';
        
        return {
            disabled: nextBtn.disabled,
            ariaDisabled: nextBtn.getAttribute('aria-disabled'),
            className: nextBtn.className,
            textContent: nextBtn.textContent.trim(),
            outerHTML: nextBtn.outerHTML.substring(0, 300) + '...'
        };
        """)
        
        print(f"🔘 Next button state: {next_btn_state}")
        
        take_screenshot(driver, debug_dir, "06_final_state_analysis")
        
    except Exception as e:
        print(f"❌ Page state analysis failed: {e}")

def main():
    """Main test function"""
    print("🧪 ADP Username Input Test")
    print("=" * 40)
    
    username = input("Enter ADP Username: ").strip()
    if not username:
        print("❌ Username required")
        return
    
    driver, wait, debug_dir = setup_driver()
    
    try:
        # Navigate to ADP
        print("🌐 Navigating to ADP...")
        driver.get("https://online.adp.com/olp/olplanding.html?APPID=ACCOUNTANTCONNECT&lightbrand=accountantconnect&lightbrand=accountantconnect")
        time.sleep(20)
        
        print(f"📍 Current URL: {driver.current_url}")
        
        # Test username input methods
        success = test_username_input_methods(driver, debug_dir, username)
        
        if success:
            print("\n🎉 SUCCESS: Next button was enabled!")
            
            # Try to click Next button
            try:
                next_btn = driver.find_element(By.ID, "verifUseridBtn")
                next_btn.click()
                print("✅ Clicked Next button")
                
                time.sleep(5)
                take_screenshot(driver, debug_dir, "07_after_next_click")
                
                print(f"📍 URL after Next: {driver.current_url}")
                
            except Exception as e:
                print(f"❌ Failed to click Next: {e}")
        else:
            print("\n❌ FAILED: Could not enable Next button with any method")
        
        # Analyze final state
        analyze_page_state(driver, debug_dir)
        
        # Keep browser open for inspection
        print(f"\n📁 Debug files saved to: {debug_dir}")
        print("🔍 Browser will stay open for 30 seconds for inspection...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        take_screenshot(driver, debug_dir, "error_state")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
