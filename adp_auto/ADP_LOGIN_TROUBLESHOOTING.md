# ADP Login Troubleshooting Guide

## 🚨 Common ADP Login Issues & Solutions

### 1. **Enable Debug Mode First**

Add to your `site_config.json`:
```json
{
  "adp_debug_mode": true,
  "adp_headless_mode": false
}
```

This will:
- Save screenshots at each step
- Save HTML page source
- Show the browser window (for visual debugging)
- Provide detailed logging

### 2. **Run the Debug Script**

```bash
cd apps/adp_auto
python3 debug_adp_login.py
```

Choose option 1 to analyze the page structure without credentials, or option 2 to test with your actual credentials.

### 3. **Common Issues & Solutions**

#### **Issue: "Could not find username field"**

**Possible Causes:**
- ADP changed their login page structure
- Page is loading slowly
- Login form is inside an iframe
- JavaScript hasn't loaded the form yet

**Solutions:**
```bash
# 1. Check if page loads correctly
python3 debug_adp_login.py  # Option 1

# 2. Increase wait time in the code
# Edit adp_settings.py and increase time.sleep(3) to time.sleep(10)

# 3. Check for iframes - the debug script will show if any exist
```

#### **Issue: "Login failed - still on login page"**

**Possible Causes:**
- Incorrect credentials
- 2FA/MFA is required
- Account is locked
- CAPTCHA is present
- ADP requires additional verification

**Solutions:**
1. **Verify credentials manually:**
   ```bash
   # Test with debug script option 2
   python3 debug_adp_login.py
   ```

2. **Check for 2FA requirements:**
   - Log into ADP manually in a browser
   - Check if 2FA is enabled on your account
   - Currently, the automation doesn't support 2FA

3. **Look for error messages:**
   - Check the debug screenshots
   - Look in the HTML files for error text

#### **Issue: "Timeout during ADP login"**

**Possible Causes:**
- Slow internet connection
- ADP servers are slow
- Page is taking too long to load

**Solutions:**
```python
# Increase timeout in adp_settings.py
self.wait = WebDriverWait(self.driver, 60)  # Increase from 30 to 60 seconds
```

#### **Issue: Chrome/ChromeDriver Issues**

**Solutions:**
```bash
# Check versions match
google-chrome --version
chromedriver --version

# Update ChromeDriver (macOS)
brew upgrade chromedriver

# Update ChromeDriver (Ubuntu)
# Run the setup script again
./setup_platform.sh
```

### 4. **Manual Testing Steps**

1. **Test manually in browser:**
   - Open https://ngapps.adp.com/apps/accountantconnect/
   - Try logging in with your credentials
   - Note any additional steps required

2. **Check network connectivity:**
   ```bash
   curl -I https://ngapps.adp.com/apps/accountantconnect/
   ```

3. **Test with different Chrome options:**
   ```json
   {
     "adp_chrome_options": [
       "--no-sandbox",
       "--disable-dev-shm-usage",
       "--disable-web-security",
       "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
     ]
   }
   ```

### 5. **Debug File Locations**

When debug mode is enabled, files are saved to:
```
sites/[your-site]/private/files/adp_downloads/debug_screenshots/
sites/[your-site]/private/files/adp_downloads/debug_html/
```

Or when using the debug script:
```
apps/adp_auto/adp_debug/
```

### 6. **Specific ADP Issues**

#### **ADP Requires Additional Verification**
- Some ADP accounts require email verification
- Check your email for verification codes
- This may require manual intervention

#### **ADP Changed Login Page**
- ADP occasionally updates their login page
- The debug script will help identify new element selectors
- Update the selectors in `adp_settings.py`

#### **Corporate Network Restrictions**
- Some corporate networks block automated access
- Try from a different network
- Check with your IT department

### 7. **Advanced Debugging**

#### **Enable Selenium Logging**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### **Use Browser Developer Tools**
1. Run with `adp_headless_mode: false`
2. Open browser developer tools (F12)
3. Watch network requests during login
4. Check for JavaScript errors

#### **Check for Dynamic Content**
```python
# Add to login method
WebDriverWait(driver, 30).until(
    lambda driver: driver.execute_script("return document.readyState") == "complete"
)
```

### 8. **Alternative Approaches**

If standard login fails, try:

1. **Different User Agent:**
   ```python
   chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
   ```

2. **Disable Images (faster loading):**
   ```python
   prefs = {"profile.managed_default_content_settings.images": 2}
   chrome_options.add_experimental_option("prefs", prefs)
   ```

3. **Use Different Chrome Profile:**
   ```python
   chrome_options.add_argument("--user-data-dir=/tmp/chrome_profile")
   ```

### 9. **Getting Help**

When reporting issues, include:
1. Debug screenshots
2. HTML files from debug directory
3. Full error logs
4. Your platform (macOS/Ubuntu)
5. Chrome and ChromeDriver versions
6. Whether manual login works

### 10. **Quick Fixes to Try**

```bash
# 1. Clear Chrome data
rm -rf /tmp/chrome_profile

# 2. Restart with clean state
bench restart

# 3. Update all dependencies
pip3 install --upgrade selenium

# 4. Test with minimal Chrome options
# Edit site_config.json to use only essential options
```

---

**Remember:** ADP login issues are often related to:
- Changed page structure
- Network/security restrictions  
- Account-specific requirements (2FA, verification)
- Browser/driver compatibility

The debug tools provided will help identify the specific issue in your case.
