# ADP Auto - Automated Timecard Import System

A Frappe application that automates the process of logging into ADP Accountant Connect, exporting timecard reports, and importing the data into your Frappe system.

## Features

- 🔐 Secure ADP login automation using Selenium WebDriver
- 📊 Automated timecard report export from ADP
- 📥 Excel file processing and data import
- 🎯 Configurable pay periods and years
- 📝 Comprehensive logging and error handling
- 🧪 Built-in testing and validation tools

## Quick Start

**🚀 For a quick setup, use our automated script:**
```bash
cd apps/adp_auto
./setup_platform.sh
```

**📖 Or see [QUICK_START.md](QUICK_START.md) for a condensed setup guide.**

## Installation

### 1. Install the app in your Frappe bench:
```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch main
bench install-app adp_auto
```

### 2. Platform-Specific Setup

#### 🍎 **macOS Setup (Demo Environment)**

**Install Chrome and ChromeDriver:**
```bash
# Install Chrome (if not already installed)
# Download from: https://www.google.com/chrome/

# Install ChromeDriver using Homebrew
brew install chromedriver

# Alternative: Download manually
# wget https://chromedriver.storage.googleapis.com/LATEST_RELEASE
# Download the appropriate version from https://chromedriver.chromium.org/
```

**Install Python dependencies:**
```bash
# Using pip
pip install selenium pandas

# Or using conda (if you use conda)
conda install selenium pandas
```

**Verify Chrome installation:**
```bash
# Check Chrome version
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version

# Check ChromeDriver
chromedriver --version
```

#### 🐧 **Ubuntu Setup (Production Environment)**

**Install Chrome and ChromeDriver:**
```bash
# Update package list
sudo apt update

# Install dependencies
sudo apt install -y wget curl unzip

# Install Google Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install -y google-chrome-stable

# Install ChromeDriver
CHROME_VERSION=$(google-chrome --version | awk '{print $3}' | cut -d. -f1)
CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION}")
wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip"
sudo unzip /tmp/chromedriver.zip -d /usr/local/bin/
sudo chmod +x /usr/local/bin/chromedriver
rm /tmp/chromedriver.zip
```

**Install Python dependencies:**
```bash
# Install pip if not available
sudo apt install -y python3-pip

# Install required packages
pip3 install selenium pandas

# For production, also install virtual display (headless mode)
sudo apt install -y xvfb
```

**Install additional dependencies for headless operation:**
```bash
# Install fonts and other dependencies for better rendering
sudo apt install -y fonts-liberation libappindicator3-1 libasound2 libatk-bridge2.0-0 \
libdrm2 libgtk-3-0 libnspr4 libnss3 libx11-xcb1 libxcomposite1 libxdamage1 \
libxrandr2 xdg-utils libxss1 libgconf-2-4
```

### 3. Verify Installation

**Test Chrome and ChromeDriver:**
```bash
# Test Chrome (macOS)
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --headless --dump-dom https://www.google.com > /dev/null && echo "Chrome OK"

# Test Chrome (Ubuntu)
google-chrome --headless --dump-dom https://www.google.com > /dev/null && echo "Chrome OK"

# Test ChromeDriver (both platforms)
chromedriver --version
```

**Test Python packages:**
```bash
python3 -c "import selenium, pandas; print('Python packages OK')"
```

## Configuration

### ADP Settings

1. Go to **ADP Settings** in your Frappe system
2. Configure the following fields:
   - **User ID**: Your ADP Accountant Connect username
   - **Password**: Your ADP Accountant Connect password
   - **Pay Year**: The year for which to export timecards (e.g., 2025)
   - **Pay Period**: The pay period frequency (Weekly/Bi-Weekly/Monthly)

### Platform-Specific Configuration

#### 🍎 **macOS Configuration (Demo)**

**For demo purposes, you can run with GUI visible:**
```json
{
  "adp_headless_mode": false
}
```

**Optional: Enable headless mode for macOS:**
```json
{
  "adp_headless_mode": true,
  "adp_chrome_options": [
    "--no-sandbox",
    "--disable-dev-shm-usage"
  ]
}
```

#### 🐧 **Ubuntu Configuration (Production)**

**For production servers, always use headless mode:**
```json
{
  "adp_headless_mode": true,
  "adp_chrome_options": [
    "--headless",
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--disable-extensions",
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding"
  ]
}
```

**For servers without display (recommended):**
```json
{
  "adp_headless_mode": true,
  "adp_use_xvfb": true,
  "adp_chrome_options": [
    "--headless",
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--window-size=1920,1080",
    "--disable-extensions",
    "--disable-web-security"
  ]
}
```

### Environment Variables (Alternative Configuration)

You can also use environment variables instead of site_config.json:

```bash
# macOS Demo
export ADP_HEADLESS_MODE=false
export ADP_DEMO_MODE=true

# Ubuntu Production
export ADP_HEADLESS_MODE=true
export ADP_PRODUCTION_MODE=true
export DISPLAY=:99  # For Xvfb
```

## Usage

### Through Frappe UI

1. **Configure Settings**: Set up your ADP credentials in ADP Settings
2. **Test Connection**: Use the "Test Connection" button to verify your credentials
3. **Run Automation**: Click "Run Automation" to start the full process

### Through API

```python
import frappe

# Test connection
result = frappe.call("adp_auto.adp_auto.doctype.adp_settings.adp_settings.test_adp_connection")

# Run automation
result = frappe.call("adp_auto.adp_auto.doctype.adp_settings.adp_settings.run_adp_automation")
```

### Command Line Testing

Run the test script to validate your setup:
```bash
cd apps/adp_auto
python test_automation.py
```

## How It Works

1. **Login**: Automated login to ADP Accountant Connect
2. **Navigation**: Navigate to Clients → KL HEALTH → Payroll
3. **Report Configuration**: Configure timecard report parameters
4. **Export**: Export timecard data to Excel
5. **Processing**: Clean and process the Excel data
6. **Import**: Create Timecard Entry records in Frappe

## Data Structure

### Timecard Entry DocType

The imported data is stored in the `Timecard Entry` DocType with the following fields:

- **Employee Name**: Name of the employee
- **Pay Period**: Pay period identifier
- **Work Date**: Date of work
- **Start Time**: Work start time
- **End Time**: Work end time
- **Regular Hours**: Regular working hours
- **Overtime Hours**: Overtime hours worked
- **Double Time Hours**: Double time hours worked
- **Total Paid Hours**: Total hours to be paid
- **Details**: Additional details from ADP
- **Notes**: Any notes or comments
- **Import Date**: When the record was imported
- **Source**: Source of the data (ADP Automation)

## Troubleshooting

### Platform-Specific Issues

#### 🍎 **macOS Troubleshooting**

**ChromeDriver Permission Issues:**
```bash
# If you get "chromedriver cannot be opened" error
xattr -d com.apple.quarantine /usr/local/bin/chromedriver

# Or if installed via Homebrew
sudo xattr -d com.apple.quarantine $(which chromedriver)
```

**Chrome Path Issues:**
```bash
# Verify Chrome installation
ls -la "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

# If Chrome is in a different location, create a symlink
sudo ln -s "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" /usr/local/bin/google-chrome
```

**Python Package Issues:**
```bash
# If using multiple Python versions
python3 -m pip install selenium pandas

# For conda users
conda install -c conda-forge selenium pandas
```

#### 🐧 **Ubuntu Troubleshooting**

**Display Issues (Headless):**
```bash
# Start Xvfb for virtual display
sudo apt install xvfb
export DISPLAY=:99
Xvfb :99 -screen 0 1920x1080x24 &

# Or use xvfb-run
xvfb-run -a --server-args="-screen 0 1920x1080x24" python3 your_script.py
```

**Chrome Sandbox Issues:**
```bash
# If you get sandbox errors, run Chrome with --no-sandbox
# This is already included in the production configuration above

# For additional security, create a dedicated user
sudo useradd -m -s /bin/bash adp-automation
sudo usermod -a -G adp-automation your-frappe-user
```

**Memory Issues:**
```bash
# Increase swap if needed
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Add to /etc/fstab for persistence
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

**Font Issues (for PDF/rendering):**
```bash
# Install additional fonts
sudo apt install -y fonts-liberation fonts-dejavu-core fonts-freefont-ttf
```

### Common Issues (Both Platforms)

1. **Chrome Driver Version Mismatch**
   ```bash
   # Check versions
   google-chrome --version
   chromedriver --version

   # Update ChromeDriver if needed (Ubuntu)
   CHROME_VERSION=$(google-chrome --version | awk '{print $3}' | cut -d. -f1)
   CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION}")
   wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip"
   sudo unzip /tmp/chromedriver.zip -d /usr/local/bin/
   sudo chmod +x /usr/local/bin/chromedriver

   # Update ChromeDriver (macOS)
   brew upgrade chromedriver
   ```

2. **Login Failures**
   - Verify ADP credentials are correct
   - Check if ADP account requires 2FA (not currently supported)
   - Ensure network connectivity to ADP servers

3. **Download Issues**
   - Ensure download directory permissions are correct
   - Check available disk space
   - Verify download directory path exists

4. **Import Errors**
   - Verify Excel file format matches expected structure
   - Check for data validation errors
   - Ensure Timecard Entry DocType is properly installed

### Logs and Debugging

**Check Frappe logs:**
```bash
# Main log
tail -f logs/frappe.log

# Error log
tail -f logs/error.log

# ADP-specific logs
grep -i "adp" logs/frappe.log
```

**Enable debug mode:**
```json
{
  "developer_mode": 1,
  "adp_debug_mode": true
}
```

**Test individual components:**
```bash
# Test Chrome installation
google-chrome --headless --dump-dom https://www.google.com

# Test ChromeDriver
chromedriver --version

# Test Python packages
python3 -c "from selenium import webdriver; print('Selenium OK')"
python3 -c "import pandas; print('Pandas OK')"

# Run the test suite
cd apps/adp_auto
python3 test_automation.py
```

## Security Considerations

- Passwords are encrypted using Frappe's built-in encryption
- Downloaded files are stored in private directories
- All operations are logged for audit purposes
- Consider using environment variables for sensitive configuration

## Development

### Running Tests

```bash
# Run the test suite
python test_automation.py

# Run specific Frappe tests
bench run-tests --app adp_auto
```

### Contributing

This app uses `pre-commit` for code formatting and linting. Please [install pre-commit](https://pre-commit.com/#installation) and enable it for this repository:

```bash
cd apps/adp_auto
pre-commit install
```

Pre-commit is configured to use the following tools for checking and formatting your code:

- ruff
- eslint
- prettier
- pyupgrade

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section above
- Review Frappe logs for detailed error information
