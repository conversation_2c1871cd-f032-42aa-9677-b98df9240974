# ADP Auto - Automated Timecard Import System

A Frappe application that automates the process of logging into ADP Accountant Connect, exporting timecard reports, and importing the data into your Frappe system.

## Features

- 🔐 Secure ADP login automation using Selenium WebDriver
- 📊 Automated timecard report export from ADP
- 📥 Excel file processing and data import
- 🎯 Configurable pay periods and years
- 📝 Comprehensive logging and error handling
- 🧪 Built-in testing and validation tools

## Installation

### 1. Install the app in your Frappe bench:
```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch main
bench install-app adp_auto
```

### 2. Install Chrome WebDriver:
- Download ChromeDriver from https://chromedriver.chromium.org/
- Ensure it's in your system PATH or place it in your project directory

### 3. Install Python dependencies:
```bash
pip install selenium pandas
```

## Configuration

### ADP Settings

1. Go to **ADP Settings** in your Frappe system
2. Configure the following fields:
   - **User ID**: Your ADP Accountant Connect username
   - **Password**: Your ADP Accountant Connect password
   - **Pay Year**: The year for which to export timecards (e.g., 2025)
   - **Pay Period**: The pay period frequency (Weekly/Bi-Weekly/Monthly)

### System Configuration

Add the following to your `site_config.json` for headless operation:
```json
{
  "adp_headless_mode": true
}
```

## Usage

### Through Frappe UI

1. **Configure Settings**: Set up your ADP credentials in ADP Settings
2. **Test Connection**: Use the "Test Connection" button to verify your credentials
3. **Run Automation**: Click "Run Automation" to start the full process

### Through API

```python
import frappe

# Test connection
result = frappe.call("adp_auto.adp_auto.doctype.adp_settings.adp_settings.test_adp_connection")

# Run automation
result = frappe.call("adp_auto.adp_auto.doctype.adp_settings.adp_settings.run_adp_automation")
```

### Command Line Testing

Run the test script to validate your setup:
```bash
cd apps/adp_auto
python test_automation.py
```

## How It Works

1. **Login**: Automated login to ADP Accountant Connect
2. **Navigation**: Navigate to Clients → KL HEALTH → Payroll
3. **Report Configuration**: Configure timecard report parameters
4. **Export**: Export timecard data to Excel
5. **Processing**: Clean and process the Excel data
6. **Import**: Create Timecard Entry records in Frappe

## Data Structure

### Timecard Entry DocType

The imported data is stored in the `Timecard Entry` DocType with the following fields:

- **Employee Name**: Name of the employee
- **Pay Period**: Pay period identifier
- **Work Date**: Date of work
- **Start Time**: Work start time
- **End Time**: Work end time
- **Regular Hours**: Regular working hours
- **Overtime Hours**: Overtime hours worked
- **Double Time Hours**: Double time hours worked
- **Total Paid Hours**: Total hours to be paid
- **Details**: Additional details from ADP
- **Notes**: Any notes or comments
- **Import Date**: When the record was imported
- **Source**: Source of the data (ADP Automation)

## Troubleshooting

### Common Issues

1. **Chrome Driver Issues**
   - Ensure ChromeDriver is installed and accessible
   - Check Chrome browser version compatibility

2. **Login Failures**
   - Verify ADP credentials are correct
   - Check if ADP account requires 2FA (not currently supported)

3. **Download Issues**
   - Ensure download directory permissions are correct
   - Check available disk space

4. **Import Errors**
   - Verify Excel file format matches expected structure
   - Check for data validation errors

### Logs

Check Frappe logs for detailed error information:
```bash
tail -f logs/frappe.log
```

## Security Considerations

- Passwords are encrypted using Frappe's built-in encryption
- Downloaded files are stored in private directories
- All operations are logged for audit purposes
- Consider using environment variables for sensitive configuration

## Development

### Running Tests

```bash
# Run the test suite
python test_automation.py

# Run specific Frappe tests
bench run-tests --app adp_auto
```

### Contributing

This app uses `pre-commit` for code formatting and linting. Please [install pre-commit](https://pre-commit.com/#installation) and enable it for this repository:

```bash
cd apps/adp_auto
pre-commit install
```

Pre-commit is configured to use the following tools for checking and formatting your code:

- ruff
- eslint
- prettier
- pyupgrade

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section above
- Review Frappe logs for detailed error information
