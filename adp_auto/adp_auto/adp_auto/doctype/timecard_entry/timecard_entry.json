{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-07 17:30:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["naming_series", "employee_name", "pay_period", "work_date", "column_break_5", "start_time", "end_time", "section_break_8", "regular_hours", "overtime_hours", "double_time_hours", "total_paid_hours", "section_break_13", "details", "notes", "section_break_16", "import_date", "source"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "TC-YYYY-MM-DD-.####", "reqd": 1}, {"fieldname": "employee_name", "fieldtype": "Data", "label": "Employee Name", "reqd": 1}, {"fieldname": "pay_period", "fieldtype": "Data", "label": "Pay Period"}, {"fieldname": "work_date", "fieldtype": "Date", "label": "Work Date"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "start_time", "fieldtype": "Time", "label": "Start Time"}, {"fieldname": "end_time", "fieldtype": "Time", "label": "End Time"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Hours"}, {"fieldname": "regular_hours", "fieldtype": "Float", "label": "Regular Hours", "precision": "2"}, {"fieldname": "overtime_hours", "fieldtype": "Float", "label": "Overtime Hours", "precision": "2"}, {"fieldname": "double_time_hours", "fieldtype": "Float", "label": "Double Time Hours", "precision": "2"}, {"fieldname": "total_paid_hours", "fieldtype": "Float", "label": "Total Paid Hours", "precision": "2"}, {"fieldname": "section_break_13", "fieldtype": "Section Break", "label": "Additional Information"}, {"fieldname": "details", "fieldtype": "Text", "label": "Details"}, {"fieldname": "notes", "fieldtype": "Text", "label": "Notes"}, {"fieldname": "section_break_16", "fieldtype": "Section Break", "label": "Import Information"}, {"fieldname": "import_date", "fieldtype": "Datetime", "label": "Import Date", "read_only": 1}, {"fieldname": "source", "fieldtype": "Data", "label": "Source", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-07 17:30:00.000000", "modified_by": "Administrator", "module": "Adp Auto", "name": "Timecard Entry", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR User", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}