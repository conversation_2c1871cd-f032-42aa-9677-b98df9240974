# Copyright (c) 2025, <PERSON><PERSON><PERSON> and Contributors
# See license.txt

import frappe
from frappe.tests import IntegrationTestCase


class TestTimecardEntry(IntegrationTestCase):
	def test_timecard_entry_creation(self):
		"""Test basic timecard entry creation"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"work_date": "2025-01-01",
			"regular_hours": 8.0,
			"overtime_hours": 2.0,
			"source": "Test"
		})
		timecard.insert()

		self.assertEqual(timecard.total_paid_hours, 10.0)
		self.assertEqual(timecard.source, "Test")
		self.assertIsNotNone(timecard.import_date)

		# Cleanup
		timecard.delete()

	def test_total_hours_calculation(self):
		"""Test automatic total hours calculation"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"regular_hours": 6.0,
			"overtime_hours": 1.5,
			"double_time_hours": 0.5
		})
		timecard.validate()

		self.assertEqual(timecard.total_paid_hours, 8.0)

	def test_required_field_validation(self):
		"""Test that employee name is required"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"work_date": "2025-01-01",
			"regular_hours": 8.0
		})

		with self.assertRaises(frappe.ValidationError):
			timecard.validate()

	def test_future_date_validation(self):
		"""Test that future work dates are not allowed"""
		future_date = frappe.utils.add_days(frappe.utils.today(), 1)

		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"work_date": future_date,
			"regular_hours": 8.0
		})

		with self.assertRaises(frappe.ValidationError):
			timecard.validate()

	def test_default_source_assignment(self):
		"""Test that default source is assigned on insert"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"work_date": "2025-01-01",
			"regular_hours": 8.0
		})

		timecard.before_insert()
		self.assertEqual(timecard.source, "Manual Entry")

	def test_import_date_assignment(self):
		"""Test that import date is assigned on insert"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"work_date": "2025-01-01",
			"regular_hours": 8.0
		})

		timecard.before_insert()
		self.assertIsNotNone(timecard.import_date)

	def test_zero_hours_handling(self):
		"""Test handling of zero or None hours"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"work_date": "2025-01-01",
			"regular_hours": None,
			"overtime_hours": 0,
			"double_time_hours": None
		})

		timecard.validate()
		# Should not calculate total_paid_hours if all hours are None/0
		self.assertFalse(timecard.total_paid_hours)

	def test_mixed_hours_calculation(self):
		"""Test calculation with mixed hour types"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"work_date": "2025-01-01",
			"regular_hours": 7.5,
			"overtime_hours": 1.25,
			"double_time_hours": 0.75
		})

		timecard.validate()
		self.assertEqual(timecard.total_paid_hours, 9.5)
