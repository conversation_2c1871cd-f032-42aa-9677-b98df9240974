# Copyright (c) 2025, <PERSON><PERSON><PERSON> and Contributors
# See license.txt

import frappe
from frappe.tests import IntegrationTestCase


class TestTimecardEntry(IntegrationTestCase):
	def test_timecard_entry_creation(self):
		"""Test basic timecard entry creation"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"work_date": "2025-01-01",
			"regular_hours": 8.0,
			"overtime_hours": 2.0,
			"source": "Test"
		})
		timecard.insert()
		
		self.assertEqual(timecard.total_paid_hours, 10.0)
		self.assertEqual(timecard.source, "Test")
		
		# Cleanup
		timecard.delete()
	
	def test_total_hours_calculation(self):
		"""Test automatic total hours calculation"""
		timecard = frappe.get_doc({
			"doctype": "Timecard Entry",
			"employee_name": "Test Employee",
			"regular_hours": 6.0,
			"overtime_hours": 1.5,
			"double_time_hours": 0.5
		})
		timecard.validate()
		
		self.assertEqual(timecard.total_paid_hours, 8.0)
