# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe import _


class TimecardEntry(Document):
	def validate(self):
		"""Validate timecard entry data"""
		if not self.employee_name:
			frappe.throw(_("Employee Name is required"))
		
		if self.work_date and self.work_date > frappe.utils.today():
			frappe.throw(_("Work Date cannot be in the future"))
		
		# Calculate total hours if not provided
		if not self.total_paid_hours and (self.regular_hours or self.overtime_hours or self.double_time_hours):
			self.total_paid_hours = (self.regular_hours or 0) + (self.overtime_hours or 0) + (self.double_time_hours or 0)
	
	def before_insert(self):
		"""Set default values before inserting"""
		if not self.import_date:
			self.import_date = frappe.utils.now()
		
		if not self.source:
			self.source = "Manual Entry"
