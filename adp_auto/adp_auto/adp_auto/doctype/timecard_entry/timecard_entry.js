// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Timecard Entry", {
	refresh: function(frm) {
		// Add custom buttons or functionality if needed
		if (frm.doc.docstatus === 0) {
			// Add any custom buttons for draft documents
		}
	},
	
	regular_hours: function(frm) {
		calculate_total_hours(frm);
	},
	
	overtime_hours: function(frm) {
		calculate_total_hours(frm);
	},
	
	double_time_hours: function(frm) {
		calculate_total_hours(frm);
	}
});

function calculate_total_hours(frm) {
	let total = (frm.doc.regular_hours || 0) + (frm.doc.overtime_hours || 0) + (frm.doc.double_time_hours || 0);
	frm.set_value('total_paid_hours', total);
}
