# Copyright (c) 2025, <PERSON><PERSON><PERSON> and Contributors
# See license.txt

import frappe
from frappe.tests import IntegrationTestCase
from frappe import _


# On IntegrationTestCase, the doctype test records and all
# link-field test record dependencies are recursively loaded
# Use these module variables to add/remove to/from that list
EXTRA_TEST_RECORD_DEPENDENCIES = []  # eg. ["User"]
IGNORE_TEST_RECORD_DEPENDENCIES = []  # eg. ["User"]


class IntegrationTestADPSettings(IntegrationTestCase):
	"""
	Integration tests for ADPSettings.
	Use this class for testing interactions between multiple components.
	"""

	def setUp(self):
		"""Set up test data"""
		self.test_settings = {
			"user_id": "test_user",
			"password": "test_password",
			"pay_year": "2025",
			"pay_period": "Bi-Weekly"
		}

	def test_adp_settings_creation(self):
		"""Test ADP Settings document creation and validation"""
		# Create ADP Settings document
		settings = frappe.get_single("ADP Settings")

		# Update with test data
		for field, value in self.test_settings.items():
			setattr(settings, field, value)

		# Test validation
		try:
			settings.validate()
			self.assertTrue(True, "ADP Settings validation passed")
		except Exception as e:
			self.fail(f"ADP Settings validation failed: {str(e)}")

	def test_required_field_validation(self):
		"""Test that required fields are properly validated"""
		settings = frappe.get_single("ADP Settings")

		# Test missing user_id
		settings.user_id = ""
		settings.password = "test"
		settings.pay_year = "2025"
		settings.pay_period = "Bi-Weekly"

		with self.assertRaises(frappe.ValidationError):
			settings.validate()

	def test_pay_year_validation(self):
		"""Test pay year validation"""
		settings = frappe.get_single("ADP Settings")
		settings.user_id = "test"
		settings.password = "test"
		settings.pay_period = "Bi-Weekly"

		# Test invalid pay year
		settings.pay_year = "invalid_year"

		try:
			settings.validate_automation_settings()
			self.fail("Should have raised validation error for invalid pay year")
		except frappe.ValidationError:
			self.assertTrue(True, "Pay year validation working correctly")

	def test_automation_settings_validation(self):
		"""Test automation settings validation method"""
		settings = frappe.get_single("ADP Settings")

		# Set valid data
		for field, value in self.test_settings.items():
			setattr(settings, field, value)

		try:
			settings.validate_automation_settings()
			self.assertTrue(True, "Automation settings validation passed")
		except Exception as e:
			self.fail(f"Automation settings validation failed: {str(e)}")

	def test_log_automation_result(self):
		"""Test automation result logging"""
		settings = frappe.get_single("ADP Settings")

		# Test logging functionality
		try:
			settings.log_automation_result("Test", "Test message", 5)
			self.assertTrue(True, "Automation result logging successful")
		except Exception as e:
			self.fail(f"Automation result logging failed: {str(e)}")

	def tearDown(self):
		"""Clean up test data"""
		# Reset ADP Settings to avoid affecting other tests
		settings = frappe.get_single("ADP Settings")
		settings.user_id = ""
		settings.password = ""
		settings.pay_year = ""
		settings.pay_period = ""
