# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe import _
import os
import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException


class ADPSettings(Document):
	def validate(self):
		"""Validate the settings before saving"""
		if not self.user_id:
			frappe.throw(_("User ID is required"))
		if not self.password:
			frappe.throw(_("Password is required"))
		if not self.pay_year:
			frappe.throw(_("Pay Year is required"))
		if not self.pay_period:
			frappe.throw(_("Pay Period is required"))

	@frappe.whitelist()
	def run_automation(self):
		"""Main method to run the ADP automation process"""
		try:
			frappe.logger().info("Starting ADP automation process")

			# Validate settings before starting
			self.validate_automation_settings()

			# Initialize the automation
			automation = ADPAutomation(self)

			# Run the complete automation process
			result = automation.execute_full_process()

			if result.get("success"):
				message = _("ADP automation completed successfully. {0} records imported.").format(result.get("records_imported", 0))
				frappe.msgprint(message)

				# Log success
				self.log_automation_result("Success", message, result.get("records_imported", 0))

				return {"status": "success", "message": "Automation completed successfully", "data": result}
			else:
				error_msg = result.get("error", "Unknown error")
				self.log_automation_result("Failed", error_msg, 0)
				frappe.throw(_("ADP automation failed: {0}").format(error_msg))

		except Exception as e:
			error_msg = str(e)
			frappe.logger().error(f"ADP automation error: {error_msg}")
			self.log_automation_result("Error", error_msg, 0)
			frappe.throw(_("ADP automation failed: {0}").format(error_msg))

	def validate_automation_settings(self):
		"""Validate that all required settings are configured"""
		if not self.user_id:
			frappe.throw(_("User ID is required for automation"))
		if not self.password:
			frappe.throw(_("Password is required for automation"))
		if not self.pay_year:
			frappe.throw(_("Pay Year is required for automation"))
		if not self.pay_period:
			frappe.throw(_("Pay Period is required for automation"))

		# Validate pay year format
		try:
			year = int(self.pay_year)
			current_year = frappe.utils.now_datetime().year
			if year < 2020 or year > current_year + 1:
				frappe.throw(_("Pay Year must be between 2020 and {0}").format(current_year + 1))
		except ValueError:
			frappe.throw(_("Pay Year must be a valid 4-digit year"))

	def log_automation_result(self, status, message, records_imported=0):
		"""Log automation results for tracking"""
		try:
			# Create a simple log entry (you could create a separate DocType for this)
			frappe.logger().info(f"ADP Automation Result - Status: {status}, Message: {message}, Records: {records_imported}")

			# You could also create an "ADP Automation Log" DocType to track runs
			# automation_log = frappe.new_doc("ADP Automation Log")
			# automation_log.update({
			#     "status": status,
			#     "message": message,
			#     "records_imported": records_imported,
			#     "run_date": frappe.utils.now(),
			#     "pay_year": self.pay_year,
			#     "pay_period": self.pay_period
			# })
			# automation_log.insert()

		except Exception as e:
			frappe.logger().error(f"Error logging automation result: {str(e)}")

	@frappe.whitelist()
	def test_connection(self):
		"""Test ADP connection without running full automation"""
		try:
			frappe.logger().info("Testing ADP connection...")

			# Validate settings first
			self.validate_automation_settings()

			# Initialize automation for connection test
			automation = ADPAutomation(self)

			# Test only the login process
			if automation.setup_driver():
				login_success = automation.login_to_adp()
				automation.cleanup()

				if login_success:
					message = _("ADP connection test successful")
					frappe.msgprint(message)
					return {"status": "success", "message": message}
				else:
					error_msg = _("ADP login failed. Please check your credentials.")
					frappe.throw(error_msg)
			else:
				error_msg = _("Failed to initialize browser for connection test")
				frappe.throw(error_msg)

		except Exception as e:
			error_msg = str(e)
			frappe.logger().error(f"ADP connection test error: {error_msg}")
			frappe.throw(_("ADP connection test failed: {0}").format(error_msg))


class ADPAutomation:
	"""Main automation class for ADP report export"""

	def __init__(self, settings_doc):
		self.settings = settings_doc
		self.driver = None
		self.wait = None
		self.download_dir = None
		self.setup_logging()

	def setup_logging(self):
		"""Setup logging for the automation process"""
		self.logger = frappe.logger("adp_automation")

	def setup_driver(self):
		"""Setup Chrome WebDriver with appropriate options"""
		try:
			# Create download directory
			self.download_dir = os.path.join(frappe.get_site_path(), "private", "files", "adp_downloads")
			os.makedirs(self.download_dir, exist_ok=True)

			# Chrome options
			chrome_options = Options()

			# Get platform-specific configuration
			is_headless = frappe.conf.get("adp_headless_mode", True)  # Default to headless
			custom_options = frappe.conf.get("adp_chrome_options", [])

			# Default options for stability
			default_options = [
				"--no-sandbox",
				"--disable-dev-shm-usage",
				"--disable-gpu",
				"--window-size=1920,1080",
				"--disable-web-security",
				"--disable-features=VizDisplayCompositor"
			]

			# Add headless mode if configured
			if is_headless:
				default_options.append("--headless")
				# Additional options for headless mode
				default_options.extend([
					"--disable-extensions",
					"--disable-background-timer-throttling",
					"--disable-backgrounding-occluded-windows",
					"--disable-renderer-backgrounding"
				])

			# Apply default options
			for option in default_options:
				chrome_options.add_argument(option)

			# Apply custom options from configuration
			for option in custom_options:
				chrome_options.add_argument(option)

			# Platform-specific adjustments
			import platform
			system = platform.system().lower()

			if system == "linux":
				# Additional Linux-specific options
				chrome_options.add_argument("--disable-background-networking")
				chrome_options.add_argument("--disable-default-apps")
				chrome_options.add_argument("--disable-sync")

				# Handle Xvfb if configured
				if frappe.conf.get("adp_use_xvfb", False):
					os.environ.setdefault("DISPLAY", ":99")

			elif system == "darwin":  # macOS
				# macOS-specific options
				if not is_headless and frappe.conf.get("adp_demo_mode", False):
					# For demo mode, remove some restrictions
					chrome_options.add_argument("--disable-web-security")
					self.logger.info("Running in macOS demo mode")

			# Set download preferences
			prefs = {
				"download.default_directory": self.download_dir,
				"download.prompt_for_download": False,
				"download.directory_upgrade": True,
				"safebrowsing.enabled": True,
				"profile.default_content_settings.popups": 0,
				"profile.default_content_setting_values.automatic_downloads": 1
			}
			chrome_options.add_experimental_option("prefs", prefs)

			# Disable logging for cleaner output
			chrome_options.add_experimental_option("excludeSwitches", ["enable-logging"])
			chrome_options.add_experimental_option('useAutomationExtension', False)

			# Initialize driver
			self.driver = webdriver.Chrome(options=chrome_options)
			self.wait = WebDriverWait(self.driver, 30)

			# Set page load timeout
			self.driver.set_page_load_timeout(60)

			# Set window size for consistency
			if not is_headless:
				self.driver.set_window_size(1920, 1080)

			self.logger.info(f"WebDriver initialized successfully on {system} (headless: {is_headless})")
			return True

		except Exception as e:
			self.logger.error(f"Failed to setup WebDriver: {str(e)}")

			# Provide platform-specific error messages
			import platform
			system = platform.system().lower()

			if system == "linux":
				error_msg = _("Failed to initialize Chrome on Linux. Please ensure Chrome and ChromeDriver are installed: sudo apt install google-chrome-stable")
			elif system == "darwin":
				error_msg = _("Failed to initialize Chrome on macOS. Please ensure Chrome is installed and ChromeDriver is available: brew install chromedriver")
			else:
				error_msg = _("Failed to initialize web browser. Please ensure Chrome is installed and accessible.")

			frappe.throw(error_msg)
			return False

	def cleanup(self):
		"""Cleanup resources"""
		try:
			if self.driver:
				self.driver.quit()
				self.logger.info("WebDriver closed successfully")
		except Exception as e:
			self.logger.error(f"Error during cleanup: {str(e)}")

	def take_debug_screenshot(self, filename_suffix):
		"""Take a screenshot for debugging purposes"""
		try:
			if self.driver and frappe.conf.get("adp_debug_mode", False):
				debug_dir = os.path.join(self.download_dir, "debug_screenshots")
				os.makedirs(debug_dir, exist_ok=True)

				timestamp = frappe.utils.now().replace(" ", "_").replace(":", "-")
				filename = f"adp_debug_{timestamp}_{filename_suffix}.png"
				filepath = os.path.join(debug_dir, filename)

				self.driver.save_screenshot(filepath)
				self.logger.info(f"Debug screenshot saved: {filepath}")
		except Exception as e:
			self.logger.error(f"Failed to take debug screenshot: {str(e)}")

	def log_page_source(self, context):
		"""Log page source for debugging"""
		try:
			if self.driver and frappe.conf.get("adp_debug_mode", False):
				debug_dir = os.path.join(self.download_dir, "debug_html")
				os.makedirs(debug_dir, exist_ok=True)

				timestamp = frappe.utils.now().replace(" ", "_").replace(":", "-")
				filename = f"adp_debug_{timestamp}_{context}.html"
				filepath = os.path.join(debug_dir, filename)

				with open(filepath, 'w', encoding='utf-8') as f:
					f.write(self.driver.page_source)

				self.logger.info(f"Page source saved: {filepath}")

				# Also log a snippet to the main log
				page_snippet = self.driver.page_source[:1000] + "..." if len(self.driver.page_source) > 1000 else self.driver.page_source
				self.logger.info(f"Page source snippet for {context}: {page_snippet}")
		except Exception as e:
			self.logger.error(f"Failed to log page source: {str(e)}")

	def execute_full_process(self):
		"""Execute the complete ADP automation process"""
		try:
			# Setup WebDriver
			if not self.setup_driver():
				return {"success": False, "error": "Failed to setup WebDriver"}

			# Step 1: Login to ADP
			if not self.login_to_adp():
				return {"success": False, "error": "Failed to login to ADP"}

			# Step 2: Navigate to clients and select KL Health
			if not self.navigate_to_client():
				return {"success": False, "error": "Failed to navigate to client"}

			# Step 3: Navigate to reports and configure timecard report
			if not self.configure_timecard_report():
				return {"success": False, "error": "Failed to configure timecard report"}

			# Step 4: Export to Excel
			downloaded_file = self.export_to_excel()
			if not downloaded_file:
				return {"success": False, "error": "Failed to export to Excel"}

			# Step 5: Process and import data
			records_imported = self.process_and_import_data(downloaded_file)

			return {
				"success": True,
				"records_imported": records_imported,
				"downloaded_file": downloaded_file
			}

		except Exception as e:
			self.logger.error(f"Error in full process execution: {str(e)}")
			return {"success": False, "error": str(e)}
		finally:
			self.cleanup()

	def login_to_adp(self):
		"""Login to ADP Accountant Connect with enhanced debugging"""
		try:
			self.logger.info("Starting ADP login process")

			# Navigate to ADP login page
			self.logger.info("Navigating to ADP login page...")
			self.driver.get("https://online.adp.com/olp/olplanding.html?APPID=ACCOUNTANTCONNECT&lightbrand=accountantconnect&lightbrand=accountantconnect")

			# Take screenshot for debugging
			self.take_debug_screenshot("01_initial_page_loaded")

			# Log current URL and page title
			self.logger.info(f"Initial URL: {self.driver.current_url}")
			self.logger.info(f"Initial page title: {self.driver.title}")

			# Wait for any redirects to complete
			self.logger.info("Waiting for redirects to complete...")
			time.sleep(5)

			# Check if we were redirected
			current_url = self.driver.current_url
			self.logger.info(f"URL after redirect: {current_url}")
			self.logger.info(f"Page title after redirect: {self.driver.title}")

			# Take screenshot after redirect
			self.take_debug_screenshot("02_after_redirect")

			# Handle different ADP login URLs
			if "online.adp.com/signin" in current_url:
				self.logger.info("Detected new ADP signin page format")
				return self._handle_new_adp_signin()
			elif "ngapps.adp.com" in current_url:
				self.logger.info("Detected classic ADP login page format")
				return self._handle_classic_adp_login()
			else:
				self.logger.error(f"Unknown ADP login page format: {current_url}")
				self.take_debug_screenshot("03_unknown_format")
				return False

		except TimeoutException as e:
			self.logger.error(f"Timeout during ADP login: {str(e)}")
			self.take_debug_screenshot("23_main_timeout_error")
			return False
		except NoSuchElementException as e:
			self.logger.error(f"Login form elements not found: {str(e)}")
			self.take_debug_screenshot("24_main_element_not_found")
			return False
		except Exception as e:
			self.logger.error(f"Error during ADP login: {str(e)}")
			self.take_debug_screenshot("25_main_general_error")
			return False

	def _handle_new_adp_signin(self):
		"""Handle the new ADP signin page format (online.adp.com) - Two-step process"""
		try:
			self.logger.info("Processing new ADP signin page (two-step process)...")

			# Wait for the new login form to load
			time.sleep(3)

			# Take screenshot of the new login page
			self.take_debug_screenshot("04_new_signin_page")

			# STEP 1: Enter username and click Next
			if not self._handle_username_step():
				return False

			# STEP 2: Enter password and submit
			if not self._handle_password_step():
				return False

			# Check login result
			return self._check_login_success("new_signin")

		except Exception as e:
			self.logger.error(f"Error in new ADP signin handler: {str(e)}")
			self.take_debug_screenshot("11_new_signin_error")
			return False

	def _handle_username_step(self):
		"""Handle the username step of the new ADP signin"""
		try:
			self.logger.info("Step 1: Handling username entry...")

			# Look for the username field using the custom sdf-input component
			username_field = None

			# Try different approaches to interact with the sdf-input component
			username_selectors = [
				# Direct sdf-input element
				(By.ID, "login-form_username"),
				# Input inside sdf-input
				(By.CSS_SELECTOR, "#login-form_username input"),
				(By.CSS_SELECTOR, "sdf-input[id='login-form_username'] input"),
				# Shadow DOM approach - try to find actual input
				(By.CSS_SELECTOR, "input[autocomplete='username']"),
				(By.CSS_SELECTOR, "input[label='User ID']"),
				# Fallback selectors
				(By.XPATH, "//sdf-input[@id='login-form_username']//input"),
				(By.XPATH, "//input[@autocomplete='username']"),
			]

			for selector_type, selector_value in username_selectors:
				try:
					if selector_type == "CSS_SELECTOR":
						username_field = self.wait.until(
							EC.presence_of_element_located((By.CSS_SELECTOR, selector_value))
						)
					elif selector_type == "XPATH":
						username_field = self.wait.until(
							EC.presence_of_element_located((By.XPATH, selector_value))
						)
					else:
						username_field = self.wait.until(
							EC.presence_of_element_located((selector_type, selector_value))
						)

					self.logger.info(f"Found username field using: {selector_type}='{selector_value}'")
					break
				except TimeoutException:
					continue

			# If we can't find the input directly, try to interact with the sdf-input component
			if not username_field:
				try:
					self.logger.info("Trying to interact with sdf-input component directly...")
					sdf_input = self.wait.until(
						EC.presence_of_element_located((By.ID, "login-form_username"))
					)

					# Try JavaScript approach to set the value
					try:
						self.logger.info("Attempting JavaScript approach to set username...")
						js_script = f"""
						var sdfInput = document.getElementById('login-form_username');
						if (sdfInput) {{
							sdfInput.value = '{self.settings.user_id}';
							sdfInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
							sdfInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
							return 'success';
						}}
						return 'failed';
						"""
						result = self.driver.execute_script(js_script)
						if result == 'success':
							self.logger.info("Successfully set username via JavaScript")
							username_field = sdf_input
						else:
							raise Exception("JavaScript approach failed")
					except Exception as js_error:
						self.logger.warning(f"JavaScript approach failed: {str(js_error)}")

						# Fallback: Try to click and send keys
						sdf_input.click()
						time.sleep(1)
						sdf_input.send_keys(self.settings.user_id)
						self.logger.info("Successfully entered username via direct interaction")
						username_field = sdf_input

				except Exception as e:
					self.logger.error(f"Could not interact with sdf-input component: {str(e)}")
					self.take_debug_screenshot("05_username_not_found_new")
					self.log_page_source("new_signin_username_missing")
					return False
			else:
				# Enter username in the found input field
				self.logger.info("Entering username in found input field...")
				username_field.clear()
				username_field.send_keys(self.settings.user_id)

			# Take screenshot after entering username
			self.take_debug_screenshot("06_username_entered_new")

			# Wait for the Next button to become enabled
			time.sleep(2)

			# Look for the Next button
			next_button = None
			next_button_selectors = [
				(By.ID, "verifUseridBtn"),
				(By.CSS_SELECTOR, "sdf-button[id='verifUseridBtn']"),
				(By.CSS_SELECTOR, "button[data-testid='verifUseridBtn']"),
				(By.XPATH, "//sdf-button[@id='verifUseridBtn']"),
				(By.XPATH, "//button[contains(text(), 'Next')]"),
				(By.XPATH, "//sdf-button[contains(text(), 'Next')]"),
			]

			for selector_type, selector_value in next_button_selectors:
				try:
					if selector_type == "CSS_SELECTOR":
						next_button = self.driver.find_element(By.CSS_SELECTOR, selector_value)
					elif selector_type == "XPATH":
						next_button = self.driver.find_element(By.XPATH, selector_value)
					else:
						next_button = self.driver.find_element(selector_type, selector_value)

					self.logger.info(f"Found Next button using: {selector_type}='{selector_value}'")
					break
				except NoSuchElementException:
					continue

			if not next_button:
				self.logger.error("Could not find Next button")
				self.take_debug_screenshot("07_next_button_not_found")
				return False

			# Check if button is enabled
			is_disabled = next_button.get_attribute("disabled")
			if is_disabled:
				self.logger.warning("Next button is disabled, waiting for it to be enabled...")
				# Wait a bit more and try again
				time.sleep(3)
				is_disabled = next_button.get_attribute("disabled")
				if is_disabled:
					self.logger.error("Next button remains disabled")
					self.take_debug_screenshot("08_next_button_disabled")
					return False

			# Click the Next button
			self.logger.info("Clicking Next button...")
			try:
				next_button.click()
			except Exception as click_error:
				self.logger.warning(f"Regular click failed: {str(click_error)}, trying JavaScript click...")
				try:
					self.driver.execute_script("arguments[0].click();", next_button)
					self.logger.info("Successfully clicked Next button via JavaScript")
				except Exception as js_error:
					self.logger.error(f"JavaScript click also failed: {str(js_error)}")
					return False

			# Take screenshot after clicking Next
			self.take_debug_screenshot("09_next_button_clicked")

			# Wait for the password page to load
			time.sleep(5)

			self.logger.info("Username step completed successfully")
			return True

		except Exception as e:
			self.logger.error(f"Error in username step: {str(e)}")
			self.take_debug_screenshot("10_username_step_error")
			return False

	def _handle_password_step(self):
		"""Handle the password step of the new ADP signin"""
		try:
			self.logger.info("Step 2: Handling password entry...")

			# Take screenshot of password page
			self.take_debug_screenshot("11_password_page")

			# Wait for password field to appear
			password_field = None
			password_selectors = [
				# Look for sdf-input with password type
				(By.CSS_SELECTOR, "sdf-input[type='password'] input"),
				(By.CSS_SELECTOR, "input[type='password']"),
				(By.CSS_SELECTOR, "sdf-input[autocomplete='current-password'] input"),
				(By.CSS_SELECTOR, "input[autocomplete='current-password']"),
				# Try by ID if it exists
				(By.ID, "password"),
				(By.ID, "login-form_password"),
				# XPath approaches
				(By.XPATH, "//sdf-input[@type='password']//input"),
				(By.XPATH, "//input[@type='password']"),
			]

			for selector_type, selector_value in password_selectors:
				try:
					if selector_type == "CSS_SELECTOR":
						password_field = self.wait.until(
							EC.presence_of_element_located((By.CSS_SELECTOR, selector_value))
						)
					elif selector_type == "XPATH":
						password_field = self.wait.until(
							EC.presence_of_element_located((By.XPATH, selector_value))
						)
					else:
						password_field = self.wait.until(
							EC.presence_of_element_located((selector_type, selector_value))
						)

					self.logger.info(f"Found password field using: {selector_type}='{selector_value}'")
					break
				except TimeoutException:
					continue

			# If we can't find password field, try to find any sdf-input and interact with it
			if not password_field:
				try:
					self.logger.info("Looking for any sdf-input component on password page...")
					sdf_inputs = self.driver.find_elements(By.TAG_NAME, "sdf-input")
					for sdf_input in sdf_inputs:
						input_type = sdf_input.get_attribute("type")
						if input_type == "password" or "password" in str(sdf_input.get_attribute("outerHTML")).lower():
							sdf_input.click()
							time.sleep(1)
							sdf_input.send_keys(self.settings.get_password("password"))
							password_field = sdf_input
							self.logger.info("Successfully entered password via sdf-input component")
							break

					if not password_field:
						self.logger.error("Could not find password field")
						self.take_debug_screenshot("12_password_not_found")
						self.log_page_source("password_step_missing")
						return False

				except Exception as e:
					self.logger.error(f"Error finding password field: {str(e)}")
					return False
			else:
				# Enter password in found field
				self.logger.info("Entering password...")
				password_field.clear()
				password_field.send_keys(self.settings.get_password("password"))

			# Take screenshot after entering password
			self.take_debug_screenshot("13_password_entered")

			# Look for submit/sign in button
			submit_button = None
			submit_selectors = [
				(By.XPATH, "//sdf-button[contains(text(), 'Sign In')]"),
				(By.XPATH, "//button[contains(text(), 'Sign In')]"),
				(By.XPATH, "//sdf-button[contains(text(), 'Submit')]"),
				(By.XPATH, "//button[contains(text(), 'Submit')]"),
				(By.CSS_SELECTOR, "button[type='submit']"),
				(By.CSS_SELECTOR, "sdf-button[type='submit']"),
				(By.ID, "submitBtn"),
				(By.ID, "signInBtn"),
			]

			for selector_type, selector_value in submit_selectors:
				try:
					if selector_type == "CSS_SELECTOR":
						submit_button = self.driver.find_element(By.CSS_SELECTOR, selector_value)
					elif selector_type == "XPATH":
						submit_button = self.driver.find_element(By.XPATH, selector_value)
					else:
						submit_button = self.driver.find_element(selector_type, selector_value)

					self.logger.info(f"Found submit button using: {selector_type}='{selector_value}'")
					break
				except NoSuchElementException:
					continue

			if not submit_button:
				self.logger.error("Could not find submit button")
				self.take_debug_screenshot("14_submit_button_not_found")
				return False

			# Click submit button
			self.logger.info("Clicking submit button...")
			submit_button.click()

			# Take screenshot after clicking submit
			self.take_debug_screenshot("15_submit_clicked")

			# Wait for response
			time.sleep(8)

			self.logger.info("Password step completed")
			return True

		except Exception as e:
			self.logger.error(f"Error in password step: {str(e)}")
			self.take_debug_screenshot("16_password_step_error")
			return False

	def _handle_classic_adp_login(self):
		"""Handle the classic ADP login page format (ngapps.adp.com)"""
		try:
			self.logger.info("Processing classic ADP login page...")

			# Wait for the classic login form to load
			time.sleep(3)

			# Take screenshot of classic login page
			self.take_debug_screenshot("12_classic_login_page")

			# Classic ADP login selectors
			username_field = None
			password_field = None
			login_button = None

			# Try to find username field (classic selectors)
			classic_username_selectors = [
				(By.ID, "username"),
				(By.ID, "user"),
				(By.ID, "userId"),
				(By.NAME, "username"),
				(By.NAME, "user"),
				(By.XPATH, "//input[@type='text' or @type='email']"),
				(By.CSS_SELECTOR, "input[placeholder*='username' i]"),
				(By.CSS_SELECTOR, "input[placeholder*='user' i]")
			]

			for selector_type, selector_value in classic_username_selectors:
				try:
					username_field = self.wait.until(
						EC.presence_of_element_located((selector_type, selector_value))
					)
					self.logger.info(f"Found username field using: {selector_type}='{selector_value}'")
					break
				except TimeoutException:
					continue

			if not username_field:
				self.logger.error("Could not find username field on classic login page")
				self.take_debug_screenshot("13_username_not_found_classic")
				return False

			# Enter username
			username_field.clear()
			username_field.send_keys(self.settings.user_id)

			# Find password field
			password_field = self.driver.find_element(By.ID, "password")
			password_field.clear()
			password_field.send_keys(self.settings.get_password("password"))

			# Find and click login button
			login_button = self.driver.find_element(By.ID, "loginButton")
			login_button.click()

			# Take screenshot after login attempt
			self.take_debug_screenshot("14_classic_login_clicked")

			# Wait for response
			time.sleep(8)

			# Check login result
			return self._check_login_success("classic")

		except Exception as e:
			self.logger.error(f"Error in classic ADP login handler: {str(e)}")
			self.take_debug_screenshot("15_classic_login_error")
			return False

	def _check_login_success(self, login_type):
		"""Check if login was successful"""
		try:
			self.logger.info(f"Checking login success for {login_type} login...")

			# Log current state
			current_url = self.driver.current_url
			page_title = self.driver.title

			self.logger.info(f"URL after login: {current_url}")
			self.logger.info(f"Title after login: {page_title}")

			# Take screenshot of result page
			self.take_debug_screenshot(f"16_{login_type}_login_result")

			# Check for various success indicators
			success_indicators = [
				"dashboard",
				"welcome",
				"home",
				"main",
				"clients",
				"accountant connect",
				"runpayroll.adp.com",
				"protected/auth.aspx"
			]

			error_indicators = [
				"error",
				"invalid",
				"incorrect",
				"failed",
				"denied",
				"locked",
				"suspended",
				"signin",
				"login"
			]

			page_text = self.driver.page_source.lower()
			current_url_lower = current_url.lower()

			# Check for error messages first
			for error_term in error_indicators:
				if error_term in page_text and error_term not in ["runpayroll", "protected"]:
					# Skip false positives
					if error_term == "login" and "runpayroll" in current_url_lower:
						continue
					if error_term == "signin" and "runpayroll" in current_url_lower:
						continue

					self.logger.error(f"Found error indicator: '{error_term}' in page")
					self.take_debug_screenshot(f"17_{login_type}_error_detected")
					return False

			# Check for success indicators
			success_found = False
			for success_term in success_indicators:
				if success_term in page_text or success_term in current_url_lower:
					self.logger.info(f"Found success indicator: '{success_term}'")
					success_found = True
					break

			# Additional URL-based checks
			success_urls = [
				"runpayroll.adp.com",
				"protected/auth.aspx",
				"accountantconnect"
			]

			for success_url in success_urls:
				if success_url in current_url_lower and "signin" not in current_url_lower:
					self.logger.info(f"Success URL detected: {success_url}")
					success_found = True
					break

			if success_found:
				self.logger.info("Login appears successful!")
				self.take_debug_screenshot(f"18_{login_type}_success")
				return True
			else:
				self.logger.error("Login failed - no success indicators found")
				self.take_debug_screenshot(f"19_{login_type}_failed")
				self.log_page_source(f"{login_type}_login_failed")
				return False

		except Exception as e:
			self.logger.error(f"Error checking login success: {str(e)}")
			return False

		except TimeoutException as e:
			self.logger.error(f"Timeout during ADP login: {str(e)}")
			self.take_debug_screenshot("23_main_timeout_error")
			return False
		except NoSuchElementException as e:
			self.logger.error(f"Login form elements not found: {str(e)}")
			self.take_debug_screenshot("24_main_element_not_found")
			return False
		except Exception as e:
			self.logger.error(f"Error during ADP login: {str(e)}")
			self.take_debug_screenshot("25_main_general_error")
			return False

	def navigate_to_client(self):
		"""Navigate to Clients section and select KL Health payroll link"""
		try:
			self.logger.info("Navigating to clients section")

			# Click on Clients in left menu
			clients_link = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Clients']"))
			)
			clients_link.click()

			# Wait for clients page to load
			time.sleep(3)

			# Search for KL HEALTH client
			search_box = self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//input[@placeholder='Name, ID, Company or Branch code']"))
			)
			search_box.clear()
			search_box.send_keys("KL HEALTH")

			# Wait for search results
			time.sleep(2)

			# Wait for KL HEALTH row to be present
			self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//td[contains(text(), 'KL HEALTH')]"))
			)

			# Click on Payroll link for KL HEALTH
			payroll_link = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//td[contains(text(), 'KL HEALTH')]/following-sibling::td//a[contains(text(), 'Payroll')]"))
			)
			payroll_link.click()

			# Switch to new window/tab
			self.driver.switch_to.window(self.driver.window_handles[-1])

			# Wait for payroll app to load
			self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//span[text()='KL HEALTH']"))
			)

			self.logger.info("Successfully navigated to KL HEALTH payroll")
			return True

		except TimeoutException:
			self.logger.error("Timeout during client navigation")
			return False
		except Exception as e:
			self.logger.error(f"Error during client navigation: {str(e)}")
			return False

	def configure_timecard_report(self):
		"""Navigate to Reports > Run single reports > Timecard and configure options"""
		try:
			self.logger.info("Configuring timecard report")

			# Click on Reports in left menu
			reports_link = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Reports']"))
			)
			reports_link.click()

			# Click on "Run single reports"
			run_single_reports = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), 'Run single reports')]"))
			)
			run_single_reports.click()

			# Wait for reports page to load
			time.sleep(3)

			# Expand Time reports section
			time_reports_section = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Time reports']"))
			)
			time_reports_section.click()

			# Click on Timecard report
			timecard_report = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Timecard']"))
			)
			timecard_report.click()

			# Wait for timecard configuration page to load
			time.sleep(3)

			# Configure Year
			year_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'year') or @aria-label='Year']"))
			))
			year_dropdown.select_by_visible_text(str(self.settings.pay_year))

			# Configure Pay Frequency (Biweekly)
			frequency_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'frequency') or @aria-label='Pay Frequency']"))
			))
			frequency_dropdown.select_by_visible_text("Biweekly")

			# Configure Pay Period (based on settings)
			period_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'period') or @aria-label='Pay Period']"))
			))
			# This will need to be configured based on the specific pay period from settings
			# For now, we'll select the first available option
			period_dropdown.select_by_index(1)

			# Set Employees to All
			employees_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'employees') or @aria-label='Employees']"))
			))
			employees_dropdown.select_by_visible_text("All")

			# Set Format to Continuous
			format_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'format') or @aria-label='Format']"))
			))
			format_dropdown.select_by_visible_text("Continuous")

			# Click Apply Changes
			apply_button = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Apply Changes')]"))
			)
			apply_button.click()

			# Wait for report to load
			time.sleep(5)

			self.logger.info("Successfully configured timecard report")
			return True

		except TimeoutException:
			self.logger.error("Timeout during timecard report configuration")
			return False
		except Exception as e:
			self.logger.error(f"Error during timecard report configuration: {str(e)}")
			return False

	def export_to_excel(self):
		"""Export the timecard report to Excel and handle download"""
		try:
			self.logger.info("Starting Excel export")

			# Click Export to Excel button
			export_button = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Export To Excel')]"))
			)
			export_button.click()

			# Wait for download to complete
			self.logger.info("Waiting for file download to complete")
			downloaded_file = self.wait_for_download()

			if downloaded_file:
				self.logger.info(f"Successfully downloaded file: {downloaded_file}")
				return downloaded_file
			else:
				self.logger.error("Failed to download Excel file")
				return None

		except TimeoutException:
			self.logger.error("Timeout during Excel export")
			return None
		except Exception as e:
			self.logger.error(f"Error during Excel export: {str(e)}")
			return None

	def wait_for_download(self, timeout=60):
		"""Wait for file download to complete"""
		try:
			start_time = time.time()

			while time.time() - start_time < timeout:
				# Check for downloaded files
				files = os.listdir(self.download_dir)
				excel_files = [f for f in files if f.endswith('.xlsx') and not f.endswith('.crdownload')]

				if excel_files:
					# Return the most recent Excel file
					excel_files.sort(key=lambda x: os.path.getctime(os.path.join(self.download_dir, x)), reverse=True)
					return os.path.join(self.download_dir, excel_files[0])

				time.sleep(2)

			return None

		except Exception as e:
			self.logger.error(f"Error waiting for download: {str(e)}")
			return None

	def process_and_import_data(self, excel_file_path):
		"""Process the downloaded Excel file and import data into Frappe"""
		try:
			self.logger.info(f"Processing Excel file: {excel_file_path}")

			# Read the Excel file
			excel_data = pd.ExcelFile(excel_file_path)

			# Check if 'Details' sheet exists
			if 'Details' not in excel_data.sheet_names:
				self.logger.error("Details sheet not found in Excel file")
				return 0

			# Read the Details sheet
			df = pd.read_excel(excel_file_path, sheet_name='Details')

			# Clean and process the data
			df = self.clean_timecard_data(df)

			# Import data into Frappe
			records_imported = self.import_timecard_data(df)

			self.logger.info(f"Successfully imported {records_imported} records")
			return records_imported

		except Exception as e:
			self.logger.error(f"Error processing Excel file: {str(e)}")
			return 0

	def clean_timecard_data(self, df):
		"""Clean and standardize the timecard data"""
		try:
			# Remove empty rows
			df = df.dropna(how='all')

			# Standardize column names
			column_mapping = {
				'Employee': 'employee_name',
				'Pay Period': 'pay_period',
				'Total Paid Hours': 'total_paid_hours',
				'Date': 'work_date',
				'Start Work': 'start_time',
				'End Work': 'end_time',
				'Regular': 'regular_hours',
				'Overtime': 'overtime_hours',
				'Double Time': 'double_time_hours',
				'Details': 'details',
				'Notes': 'notes'
			}

			# Rename columns if they exist
			for old_name, new_name in column_mapping.items():
				if old_name in df.columns:
					df = df.rename(columns={old_name: new_name})

			# Convert date columns
			if 'work_date' in df.columns:
				df['work_date'] = pd.to_datetime(df['work_date'], errors='coerce')

			# Convert time columns
			for time_col in ['start_time', 'end_time']:
				if time_col in df.columns:
					df[time_col] = pd.to_datetime(df[time_col], errors='coerce').dt.time

			# Convert numeric columns
			numeric_cols = ['total_paid_hours', 'regular_hours', 'overtime_hours', 'double_time_hours']
			for col in numeric_cols:
				if col in df.columns:
					df[col] = pd.to_numeric(df[col], errors='coerce')

			return df

		except Exception as e:
			self.logger.error(f"Error cleaning timecard data: {str(e)}")
			return df

	def import_timecard_data(self, df):
		"""Import cleaned timecard data into Frappe database"""
		try:
			records_imported = 0

			for index, row in df.iterrows():
				try:
					# Create a new Timecard Entry document
					timecard_entry = frappe.new_doc("Timecard Entry")

					# Map data to document fields
					timecard_entry.employee_name = row.get('employee_name', '')
					timecard_entry.pay_period = row.get('pay_period', '')
					timecard_entry.work_date = row.get('work_date')
					timecard_entry.start_time = row.get('start_time')
					timecard_entry.end_time = row.get('end_time')
					timecard_entry.regular_hours = row.get('regular_hours', 0)
					timecard_entry.overtime_hours = row.get('overtime_hours', 0)
					timecard_entry.double_time_hours = row.get('double_time_hours', 0)
					timecard_entry.total_paid_hours = row.get('total_paid_hours', 0)
					timecard_entry.details = row.get('details', '')
					timecard_entry.notes = row.get('notes', '')
					timecard_entry.import_date = frappe.utils.now()
					timecard_entry.source = "ADP Automation"

					# Save the document
					timecard_entry.insert()
					records_imported += 1

				except Exception as row_error:
					self.logger.error(f"Error importing row {index}: {str(row_error)}")
					continue

			frappe.db.commit()
			return records_imported

		except Exception as e:
			self.logger.error(f"Error importing timecard data: {str(e)}")
			frappe.db.rollback()
			return 0


# Whitelisted methods for API access
@frappe.whitelist()
def run_adp_automation():
	"""API method to run ADP automation"""
	try:
		settings = frappe.get_single("ADP Settings")
		if not settings:
			frappe.throw(_("ADP Settings not configured"))

		return settings.run_automation()

	except Exception as e:
		frappe.logger().error(f"API error in ADP automation: {str(e)}")
		frappe.throw(_("Failed to run ADP automation: {0}").format(str(e)))


@frappe.whitelist()
def test_adp_connection():
	"""Test ADP connection with current settings"""
	try:
		settings = frappe.get_single("ADP Settings")
		if not settings:
			frappe.throw(_("ADP Settings not configured"))

		# Initialize automation for testing
		automation = ADPAutomation(settings)

		# Test connection
		if automation.setup_driver():
			if automation.login_to_adp():
				automation.cleanup()
				return {"status": "success", "message": "Successfully connected to ADP"}
			else:
				automation.cleanup()
				return {"status": "error", "message": "Failed to login to ADP"}
		else:
			return {"status": "error", "message": "Failed to setup WebDriver"}

	except Exception as e:
		frappe.logger().error(f"Error testing ADP connection: {str(e)}")
		return {"status": "error", "message": str(e)}
