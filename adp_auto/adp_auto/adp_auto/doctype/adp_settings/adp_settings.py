# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe import _
import os
import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException


class ADPSettings(Document):
	def validate(self):
		"""Validate the settings before saving"""
		if not self.user_id:
			frappe.throw(_("User ID is required"))
		if not self.password:
			frappe.throw(_("Password is required"))
		if not self.pay_year:
			frappe.throw(_("Pay Year is required"))
		if not self.pay_period:
			frappe.throw(_("Pay Period is required"))

	@frappe.whitelist()
	def run_automation(self):
		"""Main method to run the ADP automation process"""
		try:
			frappe.logger().info("Starting ADP automation process")

			# Validate settings before starting
			self.validate_automation_settings()

			# Initialize the automation
			automation = ADPAutomation(self)

			# Run the complete automation process
			result = automation.execute_full_process()

			if result.get("success"):
				message = _("ADP automation completed successfully. {0} records imported.").format(result.get("records_imported", 0))
				frappe.msgprint(message)

				# Log success
				self.log_automation_result("Success", message, result.get("records_imported", 0))

				return {"status": "success", "message": "Automation completed successfully", "data": result}
			else:
				error_msg = result.get("error", "Unknown error")
				self.log_automation_result("Failed", error_msg, 0)
				frappe.throw(_("ADP automation failed: {0}").format(error_msg))

		except Exception as e:
			error_msg = str(e)
			frappe.logger().error(f"ADP automation error: {error_msg}")
			self.log_automation_result("Error", error_msg, 0)
			frappe.throw(_("ADP automation failed: {0}").format(error_msg))

	def validate_automation_settings(self):
		"""Validate that all required settings are configured"""
		if not self.user_id:
			frappe.throw(_("User ID is required for automation"))
		if not self.password:
			frappe.throw(_("Password is required for automation"))
		if not self.pay_year:
			frappe.throw(_("Pay Year is required for automation"))
		if not self.pay_period:
			frappe.throw(_("Pay Period is required for automation"))

		# Validate pay year format
		try:
			year = int(self.pay_year)
			current_year = frappe.utils.now_datetime().year
			if year < 2020 or year > current_year + 1:
				frappe.throw(_("Pay Year must be between 2020 and {0}").format(current_year + 1))
		except ValueError:
			frappe.throw(_("Pay Year must be a valid 4-digit year"))

	def log_automation_result(self, status, message, records_imported=0):
		"""Log automation results for tracking"""
		try:
			# Create a simple log entry (you could create a separate DocType for this)
			frappe.logger().info(f"ADP Automation Result - Status: {status}, Message: {message}, Records: {records_imported}")

			# You could also create an "ADP Automation Log" DocType to track runs
			# automation_log = frappe.new_doc("ADP Automation Log")
			# automation_log.update({
			#     "status": status,
			#     "message": message,
			#     "records_imported": records_imported,
			#     "run_date": frappe.utils.now(),
			#     "pay_year": self.pay_year,
			#     "pay_period": self.pay_period
			# })
			# automation_log.insert()

		except Exception as e:
			frappe.logger().error(f"Error logging automation result: {str(e)}")


class ADPAutomation:
	"""Main automation class for ADP report export"""

	def __init__(self, settings_doc):
		self.settings = settings_doc
		self.driver = None
		self.wait = None
		self.download_dir = None
		self.setup_logging()

	def setup_logging(self):
		"""Setup logging for the automation process"""
		self.logger = frappe.logger("adp_automation")

	def setup_driver(self):
		"""Setup Chrome WebDriver with appropriate options"""
		try:
			# Create download directory
			self.download_dir = os.path.join(frappe.get_site_path(), "private", "files", "adp_downloads")
			os.makedirs(self.download_dir, exist_ok=True)

			# Chrome options
			chrome_options = Options()

			# Get platform-specific configuration
			is_headless = frappe.conf.get("adp_headless_mode", True)  # Default to headless
			custom_options = frappe.conf.get("adp_chrome_options", [])

			# Default options for stability
			default_options = [
				"--no-sandbox",
				"--disable-dev-shm-usage",
				"--disable-gpu",
				"--window-size=1920,1080",
				"--disable-web-security",
				"--disable-features=VizDisplayCompositor"
			]

			# Add headless mode if configured
			if is_headless:
				default_options.append("--headless")
				# Additional options for headless mode
				default_options.extend([
					"--disable-extensions",
					"--disable-background-timer-throttling",
					"--disable-backgrounding-occluded-windows",
					"--disable-renderer-backgrounding"
				])

			# Apply default options
			for option in default_options:
				chrome_options.add_argument(option)

			# Apply custom options from configuration
			for option in custom_options:
				chrome_options.add_argument(option)

			# Platform-specific adjustments
			import platform
			system = platform.system().lower()

			if system == "linux":
				# Additional Linux-specific options
				chrome_options.add_argument("--disable-background-networking")
				chrome_options.add_argument("--disable-default-apps")
				chrome_options.add_argument("--disable-sync")

				# Handle Xvfb if configured
				if frappe.conf.get("adp_use_xvfb", False):
					os.environ.setdefault("DISPLAY", ":99")

			elif system == "darwin":  # macOS
				# macOS-specific options
				if not is_headless and frappe.conf.get("adp_demo_mode", False):
					# For demo mode, remove some restrictions
					chrome_options.add_argument("--disable-web-security")
					self.logger.info("Running in macOS demo mode")

			# Set download preferences
			prefs = {
				"download.default_directory": self.download_dir,
				"download.prompt_for_download": False,
				"download.directory_upgrade": True,
				"safebrowsing.enabled": True,
				"profile.default_content_settings.popups": 0,
				"profile.default_content_setting_values.automatic_downloads": 1
			}
			chrome_options.add_experimental_option("prefs", prefs)

			# Disable logging for cleaner output
			chrome_options.add_experimental_option("excludeSwitches", ["enable-logging"])
			chrome_options.add_experimental_option('useAutomationExtension', False)

			# Initialize driver
			self.driver = webdriver.Chrome(options=chrome_options)
			self.wait = WebDriverWait(self.driver, 30)

			# Set page load timeout
			self.driver.set_page_load_timeout(60)

			# Set window size for consistency
			if not is_headless:
				self.driver.set_window_size(1920, 1080)

			self.logger.info(f"WebDriver initialized successfully on {system} (headless: {is_headless})")
			return True

		except Exception as e:
			self.logger.error(f"Failed to setup WebDriver: {str(e)}")

			# Provide platform-specific error messages
			import platform
			system = platform.system().lower()

			if system == "linux":
				error_msg = _("Failed to initialize Chrome on Linux. Please ensure Chrome and ChromeDriver are installed: sudo apt install google-chrome-stable")
			elif system == "darwin":
				error_msg = _("Failed to initialize Chrome on macOS. Please ensure Chrome is installed and ChromeDriver is available: brew install chromedriver")
			else:
				error_msg = _("Failed to initialize web browser. Please ensure Chrome is installed and accessible.")

			frappe.throw(error_msg)
			return False

	def cleanup(self):
		"""Cleanup resources"""
		try:
			if self.driver:
				self.driver.quit()
				self.logger.info("WebDriver closed successfully")
		except Exception as e:
			self.logger.error(f"Error during cleanup: {str(e)}")

	def execute_full_process(self):
		"""Execute the complete ADP automation process"""
		try:
			# Setup WebDriver
			if not self.setup_driver():
				return {"success": False, "error": "Failed to setup WebDriver"}

			# Step 1: Login to ADP
			if not self.login_to_adp():
				return {"success": False, "error": "Failed to login to ADP"}

			# Step 2: Navigate to clients and select KL Health
			if not self.navigate_to_client():
				return {"success": False, "error": "Failed to navigate to client"}

			# Step 3: Navigate to reports and configure timecard report
			if not self.configure_timecard_report():
				return {"success": False, "error": "Failed to configure timecard report"}

			# Step 4: Export to Excel
			downloaded_file = self.export_to_excel()
			if not downloaded_file:
				return {"success": False, "error": "Failed to export to Excel"}

			# Step 5: Process and import data
			records_imported = self.process_and_import_data(downloaded_file)

			return {
				"success": True,
				"records_imported": records_imported,
				"downloaded_file": downloaded_file
			}

		except Exception as e:
			self.logger.error(f"Error in full process execution: {str(e)}")
			return {"success": False, "error": str(e)}
		finally:
			self.cleanup()

	def login_to_adp(self):
		"""Login to ADP Accountant Connect"""
		try:
			self.logger.info("Starting ADP login process")

			# Navigate to ADP login page
			self.driver.get("https://ngapps.adp.com/apps/accountantconnect/")

			# Wait for login form to load
			username_field = self.wait.until(
				EC.presence_of_element_located((By.ID, "username"))
			)

			# Enter credentials
			username_field.clear()
			username_field.send_keys(self.settings.user_id)

			password_field = self.driver.find_element(By.ID, "password")
			password_field.clear()
			password_field.send_keys(self.settings.get_password("password"))

			# Click login button
			login_button = self.driver.find_element(By.ID, "loginButton")
			login_button.click()

			# Wait for dashboard to load or error message
			try:
				# Try to find dashboard
				self.wait.until(
					EC.any_of(
						EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Dashboard')]")),
						EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Welcome')]")),
						EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'error') or contains(text(), 'Error')]"))
					)
				)

				# Check if login was successful
				if self.driver.current_url.find("accountantconnect") != -1 and self.driver.current_url.find("login") == -1:
					self.logger.info("Successfully logged into ADP")
					return True
				else:
					self.logger.error("Login failed - still on login page or error occurred")
					return False

			except TimeoutException:
				self.logger.error("Timeout waiting for login response")
				return False

		except TimeoutException:
			self.logger.error("Timeout during ADP login - page load timeout")
			return False
		except NoSuchElementException as e:
			self.logger.error(f"Login form elements not found: {str(e)}")
			return False
		except Exception as e:
			self.logger.error(f"Error during ADP login: {str(e)}")
			return False

	def navigate_to_client(self):
		"""Navigate to Clients section and select KL Health payroll link"""
		try:
			self.logger.info("Navigating to clients section")

			# Click on Clients in left menu
			clients_link = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Clients']"))
			)
			clients_link.click()

			# Wait for clients page to load
			time.sleep(3)

			# Search for KL HEALTH client
			search_box = self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//input[@placeholder='Name, ID, Company or Branch code']"))
			)
			search_box.clear()
			search_box.send_keys("KL HEALTH")

			# Wait for search results
			time.sleep(2)

			# Wait for KL HEALTH row to be present
			self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//td[contains(text(), 'KL HEALTH')]"))
			)

			# Click on Payroll link for KL HEALTH
			payroll_link = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//td[contains(text(), 'KL HEALTH')]/following-sibling::td//a[contains(text(), 'Payroll')]"))
			)
			payroll_link.click()

			# Switch to new window/tab
			self.driver.switch_to.window(self.driver.window_handles[-1])

			# Wait for payroll app to load
			self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//span[text()='KL HEALTH']"))
			)

			self.logger.info("Successfully navigated to KL HEALTH payroll")
			return True

		except TimeoutException:
			self.logger.error("Timeout during client navigation")
			return False
		except Exception as e:
			self.logger.error(f"Error during client navigation: {str(e)}")
			return False

	def configure_timecard_report(self):
		"""Navigate to Reports > Run single reports > Timecard and configure options"""
		try:
			self.logger.info("Configuring timecard report")

			# Click on Reports in left menu
			reports_link = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Reports']"))
			)
			reports_link.click()

			# Click on "Run single reports"
			run_single_reports = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), 'Run single reports')]"))
			)
			run_single_reports.click()

			# Wait for reports page to load
			time.sleep(3)

			# Expand Time reports section
			time_reports_section = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Time reports']"))
			)
			time_reports_section.click()

			# Click on Timecard report
			timecard_report = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//span[text()='Timecard']"))
			)
			timecard_report.click()

			# Wait for timecard configuration page to load
			time.sleep(3)

			# Configure Year
			year_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'year') or @aria-label='Year']"))
			))
			year_dropdown.select_by_visible_text(str(self.settings.pay_year))

			# Configure Pay Frequency (Biweekly)
			frequency_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'frequency') or @aria-label='Pay Frequency']"))
			))
			frequency_dropdown.select_by_visible_text("Biweekly")

			# Configure Pay Period (based on settings)
			period_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'period') or @aria-label='Pay Period']"))
			))
			# This will need to be configured based on the specific pay period from settings
			# For now, we'll select the first available option
			period_dropdown.select_by_index(1)

			# Set Employees to All
			employees_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'employees') or @aria-label='Employees']"))
			))
			employees_dropdown.select_by_visible_text("All")

			# Set Format to Continuous
			format_dropdown = Select(self.wait.until(
				EC.presence_of_element_located((By.XPATH, "//select[contains(@class, 'format') or @aria-label='Format']"))
			))
			format_dropdown.select_by_visible_text("Continuous")

			# Click Apply Changes
			apply_button = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Apply Changes')]"))
			)
			apply_button.click()

			# Wait for report to load
			time.sleep(5)

			self.logger.info("Successfully configured timecard report")
			return True

		except TimeoutException:
			self.logger.error("Timeout during timecard report configuration")
			return False
		except Exception as e:
			self.logger.error(f"Error during timecard report configuration: {str(e)}")
			return False

	def export_to_excel(self):
		"""Export the timecard report to Excel and handle download"""
		try:
			self.logger.info("Starting Excel export")

			# Click Export to Excel button
			export_button = self.wait.until(
				EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Export To Excel')]"))
			)
			export_button.click()

			# Wait for download to complete
			self.logger.info("Waiting for file download to complete")
			downloaded_file = self.wait_for_download()

			if downloaded_file:
				self.logger.info(f"Successfully downloaded file: {downloaded_file}")
				return downloaded_file
			else:
				self.logger.error("Failed to download Excel file")
				return None

		except TimeoutException:
			self.logger.error("Timeout during Excel export")
			return None
		except Exception as e:
			self.logger.error(f"Error during Excel export: {str(e)}")
			return None

	def wait_for_download(self, timeout=60):
		"""Wait for file download to complete"""
		try:
			start_time = time.time()

			while time.time() - start_time < timeout:
				# Check for downloaded files
				files = os.listdir(self.download_dir)
				excel_files = [f for f in files if f.endswith('.xlsx') and not f.endswith('.crdownload')]

				if excel_files:
					# Return the most recent Excel file
					excel_files.sort(key=lambda x: os.path.getctime(os.path.join(self.download_dir, x)), reverse=True)
					return os.path.join(self.download_dir, excel_files[0])

				time.sleep(2)

			return None

		except Exception as e:
			self.logger.error(f"Error waiting for download: {str(e)}")
			return None

	def process_and_import_data(self, excel_file_path):
		"""Process the downloaded Excel file and import data into Frappe"""
		try:
			self.logger.info(f"Processing Excel file: {excel_file_path}")

			# Read the Excel file
			excel_data = pd.ExcelFile(excel_file_path)

			# Check if 'Details' sheet exists
			if 'Details' not in excel_data.sheet_names:
				self.logger.error("Details sheet not found in Excel file")
				return 0

			# Read the Details sheet
			df = pd.read_excel(excel_file_path, sheet_name='Details')

			# Clean and process the data
			df = self.clean_timecard_data(df)

			# Import data into Frappe
			records_imported = self.import_timecard_data(df)

			self.logger.info(f"Successfully imported {records_imported} records")
			return records_imported

		except Exception as e:
			self.logger.error(f"Error processing Excel file: {str(e)}")
			return 0

	def clean_timecard_data(self, df):
		"""Clean and standardize the timecard data"""
		try:
			# Remove empty rows
			df = df.dropna(how='all')

			# Standardize column names
			column_mapping = {
				'Employee': 'employee_name',
				'Pay Period': 'pay_period',
				'Total Paid Hours': 'total_paid_hours',
				'Date': 'work_date',
				'Start Work': 'start_time',
				'End Work': 'end_time',
				'Regular': 'regular_hours',
				'Overtime': 'overtime_hours',
				'Double Time': 'double_time_hours',
				'Details': 'details',
				'Notes': 'notes'
			}

			# Rename columns if they exist
			for old_name, new_name in column_mapping.items():
				if old_name in df.columns:
					df = df.rename(columns={old_name: new_name})

			# Convert date columns
			if 'work_date' in df.columns:
				df['work_date'] = pd.to_datetime(df['work_date'], errors='coerce')

			# Convert time columns
			for time_col in ['start_time', 'end_time']:
				if time_col in df.columns:
					df[time_col] = pd.to_datetime(df[time_col], errors='coerce').dt.time

			# Convert numeric columns
			numeric_cols = ['total_paid_hours', 'regular_hours', 'overtime_hours', 'double_time_hours']
			for col in numeric_cols:
				if col in df.columns:
					df[col] = pd.to_numeric(df[col], errors='coerce')

			return df

		except Exception as e:
			self.logger.error(f"Error cleaning timecard data: {str(e)}")
			return df

	def import_timecard_data(self, df):
		"""Import cleaned timecard data into Frappe database"""
		try:
			records_imported = 0

			for index, row in df.iterrows():
				try:
					# Create a new Timecard Entry document
					timecard_entry = frappe.new_doc("Timecard Entry")

					# Map data to document fields
					timecard_entry.employee_name = row.get('employee_name', '')
					timecard_entry.pay_period = row.get('pay_period', '')
					timecard_entry.work_date = row.get('work_date')
					timecard_entry.start_time = row.get('start_time')
					timecard_entry.end_time = row.get('end_time')
					timecard_entry.regular_hours = row.get('regular_hours', 0)
					timecard_entry.overtime_hours = row.get('overtime_hours', 0)
					timecard_entry.double_time_hours = row.get('double_time_hours', 0)
					timecard_entry.total_paid_hours = row.get('total_paid_hours', 0)
					timecard_entry.details = row.get('details', '')
					timecard_entry.notes = row.get('notes', '')
					timecard_entry.import_date = frappe.utils.now()
					timecard_entry.source = "ADP Automation"

					# Save the document
					timecard_entry.insert()
					records_imported += 1

				except Exception as row_error:
					self.logger.error(f"Error importing row {index}: {str(row_error)}")
					continue

			frappe.db.commit()
			return records_imported

		except Exception as e:
			self.logger.error(f"Error importing timecard data: {str(e)}")
			frappe.db.rollback()
			return 0


# Whitelisted methods for API access
@frappe.whitelist()
def run_adp_automation():
	"""API method to run ADP automation"""
	try:
		settings = frappe.get_single("ADP Settings")
		if not settings:
			frappe.throw(_("ADP Settings not configured"))

		return settings.run_automation()

	except Exception as e:
		frappe.logger().error(f"API error in ADP automation: {str(e)}")
		frappe.throw(_("Failed to run ADP automation: {0}").format(str(e)))


@frappe.whitelist()
def test_adp_connection():
	"""Test ADP connection with current settings"""
	try:
		settings = frappe.get_single("ADP Settings")
		if not settings:
			frappe.throw(_("ADP Settings not configured"))

		# Initialize automation for testing
		automation = ADPAutomation(settings)

		# Test connection
		if automation.setup_driver():
			if automation.login_to_adp():
				automation.cleanup()
				return {"status": "success", "message": "Successfully connected to ADP"}
			else:
				automation.cleanup()
				return {"status": "error", "message": "Failed to login to ADP"}
		else:
			return {"status": "error", "message": "Failed to setup WebDriver"}

	except Exception as e:
		frappe.logger().error(f"Error testing ADP connection: {str(e)}")
		return {"status": "error", "message": str(e)}
