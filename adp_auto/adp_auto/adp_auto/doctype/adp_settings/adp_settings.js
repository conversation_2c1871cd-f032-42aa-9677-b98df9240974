// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("ADP Settings", {
	refresh(frm) {
		// Add custom buttons for ADP automation
		frm.add_custom_button(__('Test Connection'), function() {
			test_adp_connection(frm);
		}, __('ADP Actions'));

		frm.add_custom_button(__('Run Automation'), function() {
			run_adp_automation(frm);
		}, __('ADP Actions'));

		// Set button colors
		frm.page.set_inner_btn_group_as_primary(__('ADP Actions'));
	},

	validate(frm) {
		// Validate pay year format
		if (frm.doc.pay_year && !frm.doc.pay_year.match(/^\d{4}$/)) {
			frappe.msgprint(__('Pay Year must be a 4-digit year (e.g., 2025)'));
			frappe.validated = false;
		}
	}
});

function test_adp_connection(frm) {
	if (!frm.doc.user_id || !frm.doc.password) {
		frappe.msgprint(__('Please enter User ID and Password before testing connection'));
		return;
	}

	frappe.show_alert({
		message: __('Testing ADP connection...'),
		indicator: 'blue'
	});

	frappe.call({
		method: 'adp_auto.adp_auto.doctype.adp_settings.adp_settings.test_adp_connection',
		callback: function(r) {
			if (r.message) {
				if (r.message.status === 'success') {
					frappe.show_alert({
						message: r.message.message,
						indicator: 'green'
					});
				} else {
					frappe.show_alert({
						message: r.message.message,
						indicator: 'red'
					});
				}
			}
		},
		error: function(r) {
			frappe.show_alert({
				message: __('Error testing connection: ') + r.message,
				indicator: 'red'
			});
		}
	});
}

function run_adp_automation(frm) {
	// Validate required fields
	if (!frm.doc.user_id || !frm.doc.password || !frm.doc.pay_year || !frm.doc.pay_period) {
		frappe.msgprint(__('Please fill all required fields before running automation'));
		return;
	}

	// Confirm before running
	frappe.confirm(
		__('This will run the complete ADP automation process. This may take several minutes. Continue?'),
		function() {
			// Show progress dialog
			let progress_dialog = new frappe.ui.Dialog({
				title: __('ADP Automation Progress'),
				fields: [
					{
						fieldtype: 'HTML',
						fieldname: 'progress_html',
						options: `
							<div class="progress-container">
								<div class="progress-step">
									<div class="step-icon">⏳</div>
									<div class="step-text">Initializing automation...</div>
								</div>
							</div>
							<style>
								.progress-container { padding: 20px; }
								.progress-step {
									display: flex;
									align-items: center;
									margin: 10px 0;
									font-size: 14px;
								}
								.step-icon {
									margin-right: 10px;
									font-size: 16px;
								}
								.step-text { flex: 1; }
							</style>
						`
					}
				],
				primary_action_label: __('Close'),
				primary_action: function() {
					progress_dialog.hide();
				}
			});

			progress_dialog.show();

			// Update progress function
			function update_progress(step, status) {
				let icon = status === 'running' ? '⏳' : status === 'success' ? '✅' : '❌';
				let progress_html = progress_dialog.fields_dict.progress_html.$wrapper;
				progress_html.append(`
					<div class="progress-step">
						<div class="step-icon">${icon}</div>
						<div class="step-text">${step}</div>
					</div>
				`);
				progress_html.scrollTop(progress_html[0].scrollHeight);
			}

			// Run the automation
			frappe.call({
				method: 'adp_auto.adp_auto.doctype.adp_settings.adp_settings.run_adp_automation',
				callback: function(r) {
					if (r.message && r.message.status === 'success') {
						update_progress('Automation completed successfully!', 'success');
						frappe.show_alert({
							message: __('ADP automation completed successfully'),
							indicator: 'green'
						});

						// Refresh the form
						frm.reload_doc();
					} else {
						update_progress('Automation failed', 'error');
						frappe.show_alert({
							message: __('ADP automation failed'),
							indicator: 'red'
						});
					}
				},
				error: function(r) {
					update_progress('Error: ' + r.message, 'error');
					frappe.show_alert({
						message: __('Error running automation: ') + r.message,
						indicator: 'red'
					});
				}
			});
		}
	);
}
