#!/usr/bin/env python3
"""
Quick test script for the new ADP URL format
Tests the specific URL you mentioned to understand its structure
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def test_new_adp_url():
    """Test the new ADP URL format"""
    
    # The URL you mentioned
    new_adp_url = "https://online.adp.com/signin/v1/?APPID=AccountantConnect&productId=80e309c3-70cf-bae1-e053-3505430b5495&returnURL=https://runpayroll.adp.com/enrollment.aspx?lightbrand=accountantconnect&callingAppId=AccountantConnect&TARGET=-SM-https://runpayroll.adp.com/protected/auth.aspx?brand=45135cd7-de34-4a45-a9de-ef8c5a2d6fa6&auth=OLP&lightbrand=accountantconnect&lightbrand=accountantconnect"
    
    print("🔍 Testing New ADP URL Format")
    print("=" * 50)
    print(f"URL: {new_adp_url}")
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Create debug directory
    debug_dir = os.path.join(os.getcwd(), "new_adp_debug")
    os.makedirs(debug_dir, exist_ok=True)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Navigate directly to the new URL
        print("\n1. Navigating directly to new ADP URL...")
        driver.get(new_adp_url)
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page Title: {driver.title}")
        
        # Save screenshot
        screenshot_path = os.path.join(debug_dir, "01_new_url_direct.png")
        driver.save_screenshot(screenshot_path)
        print(f"Screenshot saved: {screenshot_path}")
        
        # Save HTML
        html_path = os.path.join(debug_dir, "01_new_url_direct.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print(f"HTML saved: {html_path}")
        
        # Analyze form elements
        print("\n2. Analyzing form elements...")
        
        # Find all input fields
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input fields:")
        
        for i, input_elem in enumerate(inputs):
            try:
                input_type = input_elem.get_attribute("type") or "text"
                input_id = input_elem.get_attribute("id") or "no-id"
                input_name = input_elem.get_attribute("name") or "no-name"
                input_placeholder = input_elem.get_attribute("placeholder") or "no-placeholder"
                input_class = input_elem.get_attribute("class") or "no-class"
                is_visible = input_elem.is_displayed()
                
                print(f"  Input {i+1}: type='{input_type}', id='{input_id}', name='{input_name}', placeholder='{input_placeholder}', class='{input_class}', visible={is_visible}")
                
                # Special attention to text/email/password fields
                if input_type in ['text', 'email', 'password']:
                    print(f"    ⭐ IMPORTANT: {input_type} field found!")
                    
            except Exception as e:
                print(f"  Input {i+1}: Error reading attributes - {e}")
        
        # Find all buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\nFound {len(buttons)} buttons:")
        
        for i, button in enumerate(buttons):
            try:
                button_text = button.text or "no-text"
                button_id = button.get_attribute("id") or "no-id"
                button_type = button.get_attribute("type") or "button"
                button_class = button.get_attribute("class") or "no-class"
                is_visible = button.is_displayed()
                
                print(f"  Button {i+1}: text='{button_text}', id='{button_id}', type='{button_type}', class='{button_class}', visible={is_visible}")
                
                # Special attention to submit buttons or login-related buttons
                if button_type == 'submit' or 'login' in button_text.lower() or 'sign' in button_text.lower():
                    print(f"    ⭐ IMPORTANT: Login/submit button found!")
                    
            except Exception as e:
                print(f"  Button {i+1}: Error reading attributes - {e}")
        
        # Check for forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"\nFound {len(forms)} forms:")
        
        for i, form in enumerate(forms):
            try:
                form_action = form.get_attribute("action") or "no-action"
                form_method = form.get_attribute("method") or "no-method"
                form_id = form.get_attribute("id") or "no-id"
                
                print(f"  Form {i+1}: action='{form_action}', method='{form_method}', id='{form_id}'")
                
            except Exception as e:
                print(f"  Form {i+1}: Error reading attributes - {e}")
        
        # Check for iframes
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        print(f"\nFound {len(iframes)} iframes:")
        
        for i, iframe in enumerate(iframes):
            try:
                iframe_src = iframe.get_attribute("src") or "no-src"
                iframe_id = iframe.get_attribute("id") or "no-id"
                
                print(f"  Iframe {i+1}: src='{iframe_src}', id='{iframe_id}'")
                
            except Exception as e:
                print(f"  Iframe {i+1}: Error reading attributes - {e}")
        
        # Look for specific ADP-related elements
        print("\n3. Looking for ADP-specific elements...")
        
        # Common ADP selectors to test
        test_selectors = [
            ("ID", "user"),
            ("ID", "username"),
            ("ID", "userId"),
            ("ID", "password"),
            ("ID", "pwd"),
            ("NAME", "user"),
            ("NAME", "username"),
            ("NAME", "password"),
            ("CSS", "input[type='text']"),
            ("CSS", "input[type='email']"),
            ("CSS", "input[type='password']"),
            ("CSS", "button[type='submit']"),
            ("XPATH", "//input[@placeholder and contains(@placeholder, 'user')]"),
            ("XPATH", "//input[@placeholder and contains(@placeholder, 'email')]"),
            ("XPATH", "//input[@placeholder and contains(@placeholder, 'password')]"),
            ("XPATH", "//button[contains(text(), 'Sign In')]"),
            ("XPATH", "//button[contains(text(), 'Login')]"),
        ]
        
        for selector_type, selector_value in test_selectors:
            try:
                if selector_type == "ID":
                    elements = driver.find_elements(By.ID, selector_value)
                elif selector_type == "NAME":
                    elements = driver.find_elements(By.NAME, selector_value)
                elif selector_type == "CSS":
                    elements = driver.find_elements(By.CSS_SELECTOR, selector_value)
                elif selector_type == "XPATH":
                    elements = driver.find_elements(By.XPATH, selector_value)
                
                if elements:
                    print(f"  ✅ Found {len(elements)} element(s) with {selector_type}='{selector_value}'")
                    for elem in elements:
                        if elem.is_displayed():
                            print(f"    - Element is visible")
                        else:
                            print(f"    - Element is hidden")
                else:
                    print(f"  ❌ No elements found with {selector_type}='{selector_value}'")
                    
            except Exception as e:
                print(f"  ❌ Error testing {selector_type}='{selector_value}': {e}")
        
        # Wait for manual inspection
        print(f"\n4. Debug files saved to: {debug_dir}")
        print("Browser will stay open for 60 seconds for manual inspection...")
        print("You can manually interact with the page to see how it behaves.")
        
        time.sleep(60)
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        
        # Save error state
        try:
            error_screenshot = os.path.join(debug_dir, "error_state.png")
            driver.save_screenshot(error_screenshot)
            print(f"Error screenshot saved: {error_screenshot}")
        except:
            pass
    
    finally:
        driver.quit()
        print(f"\n✅ Test completed. Check files in: {debug_dir}")

if __name__ == "__main__":
    test_new_adp_url()
