#!/bin/bash

# ADP Auto Platform Setup Script
# This script sets up the required dependencies for ADP automation
# Supports macOS and Ubuntu Linux

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect platform
detect_platform() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        PLATFORM="macos"
        print_status "Detected macOS"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        PLATFORM="ubuntu"
        print_status "Detected Linux (Ubuntu)"
    else
        print_error "Unsupported platform: $OSTYPE"
        exit 1
    fi
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Setup for macOS
setup_macos() {
    print_status "Setting up ADP Auto for macOS (Demo Environment)"
    
    # Check if Homebrew is installed
    if ! command_exists brew; then
        print_warning "Homebrew not found. Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    # Install Chrome if not present
    if ! command_exists google-chrome && [ ! -d "/Applications/Google Chrome.app" ]; then
        print_status "Installing Google Chrome..."
        brew install --cask google-chrome
    else
        print_success "Google Chrome is already installed"
    fi
    
    # Install ChromeDriver
    if ! command_exists chromedriver; then
        print_status "Installing ChromeDriver..."
        brew install chromedriver
        
        # Remove quarantine attribute
        print_status "Removing quarantine attribute from ChromeDriver..."
        xattr -d com.apple.quarantine $(which chromedriver) 2>/dev/null || true
    else
        print_success "ChromeDriver is already installed"
    fi
    
    # Install Python packages
    print_status "Installing Python packages..."
    pip3 install selenium pandas
    
    # Create demo configuration
    create_demo_config
    
    print_success "macOS setup completed!"
    print_status "You can now run the automation with GUI visible for demo purposes"
}

# Setup for Ubuntu
setup_ubuntu() {
    print_status "Setting up ADP Auto for Ubuntu (Production Environment)"
    
    # Update package list
    print_status "Updating package list..."
    sudo apt update
    
    # Install dependencies
    print_status "Installing system dependencies..."
    sudo apt install -y wget curl unzip python3-pip xvfb
    
    # Install Google Chrome
    if ! command_exists google-chrome; then
        print_status "Installing Google Chrome..."
        wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
        sudo apt update
        sudo apt install -y google-chrome-stable
    else
        print_success "Google Chrome is already installed"
    fi
    
    # Install ChromeDriver
    if ! command_exists chromedriver; then
        print_status "Installing ChromeDriver..."
        CHROME_VERSION=$(google-chrome --version | awk '{print $3}' | cut -d. -f1)
        CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION}")
        wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip"
        sudo unzip /tmp/chromedriver.zip -d /usr/local/bin/
        sudo chmod +x /usr/local/bin/chromedriver
        rm /tmp/chromedriver.zip
    else
        print_success "ChromeDriver is already installed"
    fi
    
    # Install additional dependencies for headless operation
    print_status "Installing additional dependencies..."
    sudo apt install -y fonts-liberation libappindicator3-1 libasound2 libatk-bridge2.0-0 \
    libdrm2 libgtk-3-0 libnspr4 libnss3 libx11-xcb1 libxcomposite1 libxdamage1 \
    libxrandr2 xdg-utils libxss1 libgconf-2-4 fonts-dejavu-core fonts-freefont-ttf
    
    # Install Python packages
    print_status "Installing Python packages..."
    pip3 install selenium pandas
    
    # Create production configuration
    create_production_config
    
    print_success "Ubuntu setup completed!"
    print_status "The system is configured for headless operation in production"
}

# Create demo configuration for macOS
create_demo_config() {
    cat > demo_site_config.json << EOF
{
  "adp_headless_mode": false,
  "adp_demo_mode": true,
  "adp_chrome_options": [
    "--no-sandbox",
    "--disable-dev-shm-usage"
  ]
}
EOF
    print_success "Created demo_site_config.json for macOS demo environment"
    print_warning "Add the contents of demo_site_config.json to your site's site_config.json"
}

# Create production configuration for Ubuntu
create_production_config() {
    cat > production_site_config.json << EOF
{
  "adp_headless_mode": true,
  "adp_production_mode": true,
  "adp_use_xvfb": true,
  "adp_chrome_options": [
    "--headless",
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--window-size=1920,1080",
    "--disable-extensions",
    "--disable-web-security",
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding"
  ]
}
EOF
    print_success "Created production_site_config.json for Ubuntu production environment"
    print_warning "Add the contents of production_site_config.json to your site's site_config.json"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    # Check Chrome
    if command_exists google-chrome || [ -d "/Applications/Google Chrome.app" ]; then
        print_success "✓ Google Chrome is installed"
        if [[ "$PLATFORM" == "macos" ]]; then
            /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version
        else
            google-chrome --version
        fi
    else
        print_error "✗ Google Chrome is not installed"
        return 1
    fi
    
    # Check ChromeDriver
    if command_exists chromedriver; then
        print_success "✓ ChromeDriver is installed"
        chromedriver --version
    else
        print_error "✗ ChromeDriver is not installed"
        return 1
    fi
    
    # Check Python packages
    if python3 -c "import selenium, pandas" 2>/dev/null; then
        print_success "✓ Python packages (selenium, pandas) are installed"
    else
        print_error "✗ Python packages are not installed"
        return 1
    fi
    
    print_success "All components verified successfully!"
}

# Main execution
main() {
    echo "=========================================="
    echo "ADP Auto Platform Setup Script"
    echo "=========================================="
    
    detect_platform
    
    case $PLATFORM in
        "macos")
            setup_macos
            ;;
        "ubuntu")
            setup_ubuntu
            ;;
        *)
            print_error "Unsupported platform"
            exit 1
            ;;
    esac
    
    verify_installation
    
    echo "=========================================="
    print_success "Setup completed successfully!"
    echo "=========================================="
    
    print_status "Next steps:"
    echo "1. Add the configuration from ${PLATFORM}_site_config.json to your Frappe site's site_config.json"
    echo "2. Configure ADP Settings in your Frappe system"
    echo "3. Test the connection using the 'Test Connection' button"
    echo "4. Run your first automation!"
    
    if [[ "$PLATFORM" == "ubuntu" ]]; then
        echo ""
        print_warning "For production servers, consider:"
        echo "- Setting up a dedicated user for automation"
        echo "- Configuring proper firewall rules"
        echo "- Setting up log rotation"
        echo "- Scheduling regular automation runs"
    fi
}

# Run main function
main "$@"
