#!/usr/bin/env python3
"""
Test script for ADP Automation
Run this script to test the ADP automation functionality
"""

import frappe
import sys
import os

def init_frappe():
    """Initialize Frappe environment"""
    try:
        # Add the current directory to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Initialize Frappe
        frappe.init(site="localhost")  # Replace with your site name
        frappe.connect()
        
        print("✅ Frappe initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize Frappe: {str(e)}")
        return False

def test_adp_settings():
    """Test ADP Settings DocType"""
    try:
        # Get or create ADP Settings
        settings = frappe.get_single("ADP Settings")
        
        if not settings:
            print("❌ ADP Settings not found")
            return False
        
        print("✅ ADP Settings found")
        
        # Check if required fields are set
        required_fields = ['user_id', 'password', 'pay_year', 'pay_period']
        missing_fields = []
        
        for field in required_fields:
            if not getattr(settings, field, None):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"⚠️  Missing required fields: {', '.join(missing_fields)}")
            print("Please configure these fields in ADP Settings before running automation")
            return False
        
        print("✅ All required fields are configured")
        return True
        
    except Exception as e:
        print(f"❌ Error testing ADP Settings: {str(e)}")
        return False

def test_connection():
    """Test ADP connection"""
    try:
        print("🔄 Testing ADP connection...")
        
        # Import the test function
        from adp_auto.adp_auto.doctype.adp_settings.adp_settings import test_adp_connection
        
        result = test_adp_connection()
        
        if result and result.get('status') == 'success':
            print("✅ ADP connection test successful")
            return True
        else:
            print(f"❌ ADP connection test failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing ADP connection: {str(e)}")
        return False

def test_timecard_entry():
    """Test Timecard Entry DocType"""
    try:
        # Create a test timecard entry
        test_entry = frappe.new_doc("Timecard Entry")
        test_entry.update({
            "employee_name": "Test Employee",
            "pay_period": "2025-01",
            "work_date": "2025-01-15",
            "regular_hours": 8.0,
            "overtime_hours": 0.0,
            "total_paid_hours": 8.0,
            "source": "Test"
        })
        
        # Validate the entry
        test_entry.validate()
        
        print("✅ Timecard Entry validation successful")
        return True
        
    except Exception as e:
        print(f"❌ Error testing Timecard Entry: {str(e)}")
        return False

def run_full_automation():
    """Run the full ADP automation"""
    try:
        print("🔄 Starting full ADP automation...")
        print("⚠️  This will take several minutes and requires valid ADP credentials")
        
        # Ask for confirmation
        response = input("Do you want to proceed? (y/N): ")
        if response.lower() != 'y':
            print("Automation cancelled by user")
            return False
        
        # Import and run automation
        from adp_auto.adp_auto.doctype.adp_settings.adp_settings import run_adp_automation
        
        result = run_adp_automation()
        
        if result and result.get('status') == 'success':
            print("✅ ADP automation completed successfully")
            print(f"📊 Records imported: {result.get('data', {}).get('records_imported', 0)}")
            return True
        else:
            print(f"❌ ADP automation failed: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error running ADP automation: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 ADP Automation Test Suite")
    print("=" * 50)
    
    # Initialize Frappe
    if not init_frappe():
        return
    
    # Test ADP Settings
    print("\n1. Testing ADP Settings...")
    if not test_adp_settings():
        print("Please configure ADP Settings before proceeding")
        return
    
    # Test Timecard Entry
    print("\n2. Testing Timecard Entry...")
    test_timecard_entry()
    
    # Test ADP Connection
    print("\n3. Testing ADP Connection...")
    connection_ok = test_connection()
    
    if connection_ok:
        print("\n4. Full Automation Test")
        print("Would you like to run the full automation?")
        run_full_automation()
    else:
        print("\n❌ Skipping full automation test due to connection issues")
    
    print("\n" + "=" * 50)
    print("🏁 Test suite completed")

if __name__ == "__main__":
    main()
