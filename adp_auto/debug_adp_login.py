#!/usr/bin/env python3
"""
ADP Login Debug Script
This script helps debug ADP login issues by providing detailed information
about the login process and page structure.
"""

import os
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_debug_driver():
    """Setup Chrome WebDriver for debugging"""
    chrome_options = Options()
    
    # For debugging, don't run headless so we can see what's happening
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Create debug directory
    debug_dir = os.path.join(os.getcwd(), "adp_debug")
    os.makedirs(debug_dir, exist_ok=True)
    
    # Set download preferences
    prefs = {
        "download.default_directory": debug_dir,
        "download.prompt_for_download": False,
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    return driver, debug_dir

def analyze_page_structure(driver, debug_dir, step_name):
    """Analyze and save page structure"""
    print(f"\n=== {step_name} ===")
    print(f"URL: {driver.current_url}")
    print(f"Title: {driver.title}")
    
    # Save screenshot
    screenshot_path = os.path.join(debug_dir, f"{step_name.lower().replace(' ', '_')}.png")
    driver.save_screenshot(screenshot_path)
    print(f"Screenshot saved: {screenshot_path}")
    
    # Save page source
    html_path = os.path.join(debug_dir, f"{step_name.lower().replace(' ', '_')}.html")
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(driver.page_source)
    print(f"HTML saved: {html_path}")
    
    # Look for form elements
    print("\n--- Form Elements Found ---")
    
    # Find all input fields
    inputs = driver.find_elements(By.TAG_NAME, "input")
    for i, input_elem in enumerate(inputs):
        try:
            input_type = input_elem.get_attribute("type") or "text"
            input_id = input_elem.get_attribute("id") or "no-id"
            input_name = input_elem.get_attribute("name") or "no-name"
            input_placeholder = input_elem.get_attribute("placeholder") or "no-placeholder"
            print(f"Input {i+1}: type='{input_type}', id='{input_id}', name='{input_name}', placeholder='{input_placeholder}'")
        except Exception as e:
            print(f"Input {i+1}: Error reading attributes - {e}")
    
    # Find all buttons
    buttons = driver.find_elements(By.TAG_NAME, "button")
    for i, button in enumerate(buttons):
        try:
            button_text = button.text or "no-text"
            button_id = button.get_attribute("id") or "no-id"
            button_type = button.get_attribute("type") or "button"
            print(f"Button {i+1}: text='{button_text}', id='{button_id}', type='{button_type}'")
        except Exception as e:
            print(f"Button {i+1}: Error reading attributes - {e}")
    
    # Find submit inputs
    submit_inputs = driver.find_elements(By.XPATH, "//input[@type='submit']")
    for i, submit_input in enumerate(submit_inputs):
        try:
            submit_value = submit_input.get_attribute("value") or "no-value"
            submit_id = submit_input.get_attribute("id") or "no-id"
            print(f"Submit Input {i+1}: value='{submit_value}', id='{submit_id}'")
        except Exception as e:
            print(f"Submit Input {i+1}: Error reading attributes - {e}")

def test_adp_login_structure():
    """Test ADP login page structure without actual login"""
    print("🔍 ADP Login Structure Analysis")
    print("=" * 50)
    
    driver, debug_dir = setup_debug_driver()
    
    try:
        # Navigate to ADP login page
        print("Navigating to ADP login page...")
        driver.get("https://ngapps.adp.com/apps/accountantconnect/")
        
        # Wait for page to load
        time.sleep(5)
        
        # Analyze initial page
        analyze_page_structure(driver, debug_dir, "01_Initial_Page")
        
        # Wait for any dynamic content to load
        print("\nWaiting for dynamic content...")
        time.sleep(10)
        
        # Analyze after waiting
        analyze_page_structure(driver, debug_dir, "02_After_Wait")
        
        # Try to find username field with various selectors
        print("\n=== Testing Username Field Selectors ===")
        username_selectors = [
            (By.ID, "username"),
            (By.ID, "user"),
            (By.ID, "userId"),
            (By.NAME, "username"),
            (By.NAME, "user"),
            (By.XPATH, "//input[@type='text']"),
            (By.XPATH, "//input[@type='email']"),
            (By.CSS_SELECTOR, "input[placeholder*='username' i]"),
            (By.CSS_SELECTOR, "input[placeholder*='user' i]"),
            (By.CSS_SELECTOR, "input[placeholder*='email' i]")
        ]
        
        for selector_type, selector_value in username_selectors:
            try:
                element = driver.find_element(selector_type, selector_value)
                print(f"✅ Found username field: {selector_type}='{selector_value}'")
                print(f"   Element details: tag='{element.tag_name}', visible={element.is_displayed()}")
            except NoSuchElementException:
                print(f"❌ Not found: {selector_type}='{selector_value}'")
        
        # Try to find password field
        print("\n=== Testing Password Field Selectors ===")
        password_selectors = [
            (By.ID, "password"),
            (By.ID, "pwd"),
            (By.NAME, "password"),
            (By.NAME, "pwd"),
            (By.XPATH, "//input[@type='password']"),
            (By.CSS_SELECTOR, "input[placeholder*='password' i]")
        ]
        
        for selector_type, selector_value in password_selectors:
            try:
                element = driver.find_element(selector_type, selector_value)
                print(f"✅ Found password field: {selector_type}='{selector_value}'")
                print(f"   Element details: tag='{element.tag_name}', visible={element.is_displayed()}")
            except NoSuchElementException:
                print(f"❌ Not found: {selector_type}='{selector_value}'")
        
        # Check for any iframes
        print("\n=== Checking for iframes ===")
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            print(f"Found {len(iframes)} iframe(s)")
            for i, iframe in enumerate(iframes):
                src = iframe.get_attribute("src") or "no-src"
                iframe_id = iframe.get_attribute("id") or "no-id"
                print(f"Iframe {i+1}: src='{src}', id='{iframe_id}'")
        else:
            print("No iframes found")
        
        # Keep browser open for manual inspection
        print(f"\n🔍 Debug files saved to: {debug_dir}")
        print("Browser will stay open for 60 seconds for manual inspection...")
        print("Check the screenshots and HTML files for detailed analysis.")
        
        time.sleep(60)
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        analyze_page_structure(driver, debug_dir, "03_Error_State")
    
    finally:
        driver.quit()
        print(f"\n✅ Analysis complete. Check files in: {debug_dir}")

def test_with_credentials():
    """Test login with actual credentials (interactive)"""
    print("🔐 ADP Login Test with Credentials")
    print("=" * 50)
    
    # Get credentials interactively
    username = input("Enter ADP Username: ").strip()
    if not username:
        print("❌ Username is required")
        return
    
    password = input("Enter ADP Password: ").strip()
    if not password:
        print("❌ Password is required")
        return
    
    driver, debug_dir = setup_debug_driver()
    
    try:
        # Navigate to ADP login page
        print("Navigating to ADP login page...")
        driver.get("https://ngapps.adp.com/apps/accountantconnect/")
        time.sleep(5)
        
        analyze_page_structure(driver, debug_dir, "01_Login_Page")
        
        # Try to find and fill username
        print("Looking for username field...")
        username_field = None
        username_selectors = [
            (By.ID, "username"),
            (By.ID, "user"),
            (By.NAME, "username"),
            (By.XPATH, "//input[@type='text' or @type='email']")
        ]
        
        for selector_type, selector_value in username_selectors:
            try:
                username_field = driver.find_element(selector_type, selector_value)
                print(f"✅ Found username field: {selector_type}='{selector_value}'")
                break
            except NoSuchElementException:
                continue
        
        if not username_field:
            print("❌ Could not find username field")
            return
        
        # Fill username
        username_field.clear()
        username_field.send_keys(username)
        print("✅ Username entered")
        
        # Find and fill password
        print("Looking for password field...")
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
        password_field.clear()
        password_field.send_keys(password)
        print("✅ Password entered")
        
        analyze_page_structure(driver, debug_dir, "02_Credentials_Entered")
        
        # Find and click login button
        print("Looking for login button...")
        login_button = None
        login_selectors = [
            (By.ID, "loginButton"),
            (By.ID, "login"),
            (By.XPATH, "//button[@type='submit']"),
            (By.XPATH, "//input[@type='submit']"),
            (By.XPATH, "//button[contains(text(), 'Login') or contains(text(), 'Sign In')]")
        ]
        
        for selector_type, selector_value in login_selectors:
            try:
                login_button = driver.find_element(selector_type, selector_value)
                print(f"✅ Found login button: {selector_type}='{selector_value}'")
                break
            except NoSuchElementException:
                continue
        
        if not login_button:
            print("❌ Could not find login button")
            return
        
        # Click login
        login_button.click()
        print("✅ Login button clicked")
        
        # Wait for response
        print("Waiting for login response...")
        time.sleep(10)
        
        analyze_page_structure(driver, debug_dir, "03_After_Login")
        
        # Check result
        current_url = driver.current_url.lower()
        if "login" not in current_url and "accountantconnect" in current_url:
            print("✅ Login appears successful!")
        else:
            print("❌ Login may have failed")
            print(f"Current URL: {driver.current_url}")
        
        # Keep browser open for inspection
        print("Browser will stay open for 30 seconds for inspection...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error during login test: {str(e)}")
        analyze_page_structure(driver, debug_dir, "04_Error_State")
    
    finally:
        driver.quit()

def main():
    """Main function"""
    print("ADP Login Debug Tool")
    print("=" * 30)
    print("1. Analyze login page structure (no credentials needed)")
    print("2. Test login with credentials")
    print("3. Exit")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        test_adp_login_structure()
    elif choice == "2":
        test_with_credentials()
    elif choice == "3":
        print("Goodbye!")
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()
