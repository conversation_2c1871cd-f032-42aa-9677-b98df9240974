#!/usr/bin/env python3
"""
Test script for ADP single-page login
Based on the screenshot showing both username and password fields on the same page
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    debug_dir = os.path.join(os.getcwd(), "single_page_debug")
    os.makedirs(debug_dir, exist_ok=True)
    
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 30)
    
    return driver, wait, debug_dir

def take_screenshot(driver, debug_dir, filename):
    """Take a screenshot"""
    try:
        filepath = os.path.join(debug_dir, f"{filename}.png")
        driver.save_screenshot(filepath)
        print(f"📸 Screenshot: {filepath}")
    except Exception as e:
        print(f"❌ Screenshot failed: {e}")

def save_html(driver, debug_dir, filename):
    """Save HTML source"""
    try:
        filepath = os.path.join(debug_dir, f"{filename}.html")
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print(f"💾 HTML saved: {filepath}")
    except Exception as e:
        print(f"❌ HTML save failed: {e}")

def test_single_page_login(driver, wait, debug_dir, username, password):
    """Test the single-page login process"""
    print("\n🔐 Testing Single-Page Login Process")
    print("=" * 50)
    
    # Take initial screenshot
    take_screenshot(driver, debug_dir, "01_login_page")
    save_html(driver, debug_dir, "01_login_page")
    
    # Step 1: Find and fill username field
    print("\n📝 Step 1: Entering Username")
    try:
        # Look for username field (should be visible)
        username_selectors = [
            ("CSS", "input[type='text']"),
            ("CSS", "input[type='email']"),
            ("CSS", "input[autocomplete='username']"),
            ("XPATH", "//input[@type='text' or @type='email']"),
            ("ID", "user"),
            ("ID", "username"),
            ("NAME", "user"),
        ]
        
        username_field = None
        for selector_type, selector_value in username_selectors:
            try:
                if selector_type == "CSS":
                    username_field = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector_value)))
                elif selector_type == "XPATH":
                    username_field = wait.until(EC.element_to_be_clickable((By.XPATH, selector_value)))
                elif selector_type == "ID":
                    username_field = wait.until(EC.element_to_be_clickable((By.ID, selector_value)))
                elif selector_type == "NAME":
                    username_field = wait.until(EC.element_to_be_clickable((By.NAME, selector_value)))
                
                print(f"✅ Found username field: {selector_type}='{selector_value}'")
                break
            except TimeoutException:
                print(f"❌ Not found: {selector_type}='{selector_value}'")
                continue
        
        if not username_field:
            print("❌ Could not find username field")
            return False
        
        # Enter username
        username_field.clear()
        username_field.send_keys(username)
        print(f"✅ Username entered: {username}")
        
        take_screenshot(driver, debug_dir, "02_username_entered")
        
    except Exception as e:
        print(f"❌ Username entry failed: {e}")
        return False
    
    # Step 2: Find and fill password field
    print("\n🔒 Step 2: Entering Password")
    try:
        # Look for password field
        password_selectors = [
            ("CSS", "input[type='password']"),
            ("ID", "password"),
            ("ID", "pwd"),
            ("NAME", "password"),
            ("CSS", "input[autocomplete='current-password']"),
        ]
        
        password_field = None
        for selector_type, selector_value in password_selectors:
            try:
                if selector_type == "CSS":
                    password_field = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector_value)))
                elif selector_type == "ID":
                    password_field = wait.until(EC.element_to_be_clickable((By.ID, selector_value)))
                elif selector_type == "NAME":
                    password_field = wait.until(EC.element_to_be_clickable((By.NAME, selector_value)))
                
                print(f"✅ Found password field: {selector_type}='{selector_value}'")
                break
            except TimeoutException:
                print(f"❌ Not found: {selector_type}='{selector_value}'")
                continue
        
        if not password_field:
            print("❌ Could not find password field")
            return False
        
        # Enter password
        password_field.clear()
        password_field.send_keys(password)
        print("✅ Password entered")
        
        take_screenshot(driver, debug_dir, "03_password_entered")
        
    except Exception as e:
        print(f"❌ Password entry failed: {e}")
        return False
    
    # Step 3: Find and click Sign In button
    print("\n🔘 Step 3: Clicking Sign In Button")
    try:
        # Look for sign in button
        signin_selectors = [
            ("XPATH", "//button[contains(text(), 'Sign in')]"),
            ("XPATH", "//button[contains(text(), 'Sign In')]"),
            ("XPATH", "//button[contains(text(), 'LOGIN')]"),
            ("XPATH", "//button[contains(text(), 'Login')]"),
            ("XPATH", "//input[@type='submit']"),
            ("CSS", "button[type='submit']"),
            ("ID", "submitBtn"),
            ("ID", "loginBtn"),
        ]
        
        signin_button = None
        for selector_type, selector_value in signin_selectors:
            try:
                if selector_type == "CSS":
                    signin_button = driver.find_element(By.CSS_SELECTOR, selector_value)
                elif selector_type == "XPATH":
                    signin_button = driver.find_element(By.XPATH, selector_value)
                elif selector_type == "ID":
                    signin_button = driver.find_element(By.ID, selector_value)
                
                print(f"✅ Found Sign In button: {selector_type}='{selector_value}'")
                print(f"Button text: '{signin_button.text}'")
                break
            except NoSuchElementException:
                print(f"❌ Not found: {selector_type}='{selector_value}'")
                continue
        
        if not signin_button:
            print("❌ Could not find Sign In button")
            take_screenshot(driver, debug_dir, "04_signin_button_not_found")
            return False
        
        # Click Sign In button
        signin_button.click()
        print("✅ Sign In button clicked")
        
        take_screenshot(driver, debug_dir, "05_signin_clicked")
        
        # Wait for response
        time.sleep(8)
        
    except Exception as e:
        print(f"❌ Sign In click failed: {e}")
        return False
    
    # Step 4: Check result
    print("\n🎯 Step 4: Checking Login Result")
    try:
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"📍 Current URL: {current_url}")
        print(f"📄 Page Title: {page_title}")
        
        take_screenshot(driver, debug_dir, "06_login_result")
        save_html(driver, debug_dir, "06_login_result")
        
        # Check for success indicators
        success_indicators = [
            "runpayroll.adp.com",
            "protected/auth.aspx",
            "dashboard",
            "welcome",
            "accountantconnect"
        ]
        
        error_indicators = [
            "error",
            "invalid",
            "incorrect",
            "failed",
            "denied"
        ]
        
        page_text = driver.page_source.lower()
        current_url_lower = current_url.lower()
        
        # Check for errors first
        for error_term in error_indicators:
            if error_term in page_text:
                print(f"❌ Error detected: '{error_term}' found in page")
                return False
        
        # Check for success
        for success_term in success_indicators:
            if success_term in page_text or success_term in current_url_lower:
                print(f"✅ Success indicator found: '{success_term}'")
                return True
        
        # Check URL change as success indicator
        if current_url != driver.current_url and "signin" not in current_url_lower:
            print("✅ URL changed, login likely successful")
            return True
        
        print("❓ Login result unclear - no clear success or error indicators")
        return False
        
    except Exception as e:
        print(f"❌ Result check failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 ADP Single-Page Login Test")
    print("=" * 40)
    
    # Get credentials
    username = input("Enter ADP Username: ").strip()
    if not username:
        print("❌ Username required")
        return
    
    password = input("Enter ADP Password: ").strip()
    if not password:
        print("❌ Password required")
        return
    
    driver, wait, debug_dir = setup_driver()
    
    try:
        # Navigate to ADP
        print("🌐 Navigating to ADP...")
        driver.get("https://online.adp.com/olp/olplanding.html?APPID=ACCOUNTANTCONNECT&lightbrand=accountantconnect&lightbrand=accountantconnect")
        time.sleep(5)
        
        print(f"📍 Initial URL: {driver.current_url}")
        
        # Test the login process
        success = test_single_page_login(driver, wait, debug_dir, username, password)
        
        if success:
            print("\n🎉 LOGIN SUCCESSFUL!")
        else:
            print("\n❌ LOGIN FAILED!")
        
        # Keep browser open for inspection
        print(f"\n📁 Debug files saved to: {debug_dir}")
        print("🔍 Browser will stay open for 30 seconds for inspection...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        take_screenshot(driver, debug_dir, "error_state")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
