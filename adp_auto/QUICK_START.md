# ADP Auto - Quick Start Guide

## 🚀 Platform-Specific Setup

### 🍎 macOS (Demo Environment)

**Automated Setup:**
```bash
cd apps/adp_auto
./setup_platform.sh
```

**Manual Setup:**
```bash
# Install Chrome (if not installed)
# Download from: https://www.google.com/chrome/

# Install ChromeDriver
brew install chromedriver
xattr -d com.apple.quarantine $(which chromedriver)

# Install Python packages
pip3 install selenium pandas
```

**Configuration for macOS Demo:**
Add to your `site_config.json`:
```json
{
  "adp_headless_mode": false,
  "adp_demo_mode": true,
  "adp_chrome_options": [
    "--no-sandbox",
    "--disable-dev-shm-usage"
  ]
}
```

### 🐧 Ubuntu (Production Environment)

**Automated Setup:**
```bash
cd apps/adp_auto
./setup_platform.sh
```

**Manual Setup:**
```bash
# Install Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install -y google-chrome-stable

# Install ChromeDriver
CHROME_VERSION=$(google-chrome --version | awk '{print $3}' | cut -d. -f1)
CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION}")
wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip"
sudo unzip /tmp/chromedriver.zip -d /usr/local/bin/
sudo chmod +x /usr/local/bin/chromedriver

# Install dependencies
sudo apt install -y xvfb fonts-liberation python3-pip
pip3 install selenium pandas
```

**Configuration for Ubuntu Production:**
Add to your `site_config.json`:
```json
{
  "adp_headless_mode": true,
  "adp_production_mode": true,
  "adp_use_xvfb": true,
  "adp_chrome_options": [
    "--headless",
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--window-size=1920,1080",
    "--disable-extensions",
    "--disable-web-security"
  ]
}
```

## ⚡ Quick Test

**Verify Installation:**
```bash
# Test Chrome
google-chrome --version  # Ubuntu
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version  # macOS

# Test ChromeDriver
chromedriver --version

# Test Python packages
python3 -c "import selenium, pandas; print('All packages OK')"
```

**Run Test Suite:**
```bash
cd apps/adp_auto
python3 test_automation.py
```

## 🔧 Configuration Steps

1. **Install the Frappe App:**
   ```bash
   bench get-app https://github.com/your-repo/adp_auto.git
   bench install-app adp_auto
   ```

2. **Configure ADP Settings:**
   - Go to ADP Settings in Frappe
   - Enter your ADP credentials
   - Set Pay Year and Pay Period
   - Click "Test Connection"

3. **Run First Automation:**
   - Click "Run Automation" button
   - Monitor progress in the dialog
   - Check imported Timecard Entries

## 🐛 Common Issues & Solutions

### macOS Issues:
```bash
# ChromeDriver quarantine issue
xattr -d com.apple.quarantine $(which chromedriver)

# Permission denied
sudo chmod +x /usr/local/bin/chromedriver
```

### Ubuntu Issues:
```bash
# Display issues
export DISPLAY=:99
Xvfb :99 -screen 0 1920x1080x24 &

# Chrome sandbox issues (already handled in config)
# Memory issues - increase swap if needed
```

## 📊 Usage Examples

**API Usage:**
```python
import frappe

# Test connection
result = frappe.call("adp_auto.adp_auto.doctype.adp_settings.adp_settings.test_adp_connection")

# Run automation
result = frappe.call("adp_auto.adp_auto.doctype.adp_settings.adp_settings.run_adp_automation")
```

**Command Line:**
```bash
# Run test suite
python3 test_automation.py

# Check logs
tail -f logs/frappe.log | grep -i adp
```

## 🔒 Security Notes

- **macOS Demo**: Runs with GUI visible, suitable for demonstrations
- **Ubuntu Production**: Runs headless with enhanced security options
- Passwords are encrypted using Frappe's built-in encryption
- All operations are logged for audit purposes

## 📈 Next Steps

1. **Schedule Automation**: Set up cron jobs for regular runs
2. **Monitor Logs**: Set up log monitoring and alerts
3. **Backup Data**: Regular backups of imported timecard data
4. **Scale**: Consider load balancing for high-volume operations

---

**Need Help?** Check the full README.md for detailed troubleshooting and configuration options.
