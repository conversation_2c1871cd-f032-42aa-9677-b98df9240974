#!/usr/bin/env python3
"""
Test script for the new ADP two-step login process
This script specifically tests the username -> Next -> password flow
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Create debug directory
    debug_dir = os.path.join(os.getcwd(), "two_step_debug")
    os.makedirs(debug_dir, exist_ok=True)
    
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 30)
    
    return driver, wait, debug_dir

def take_screenshot(driver, debug_dir, filename):
    """Take a screenshot"""
    try:
        filepath = os.path.join(debug_dir, f"{filename}.png")
        driver.save_screenshot(filepath)
        print(f"Screenshot saved: {filepath}")
    except Exception as e:
        print(f"Failed to take screenshot: {e}")

def save_html(driver, debug_dir, filename):
    """Save HTML source"""
    try:
        filepath = os.path.join(debug_dir, f"{filename}.html")
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print(f"HTML saved: {filepath}")
    except Exception as e:
        print(f"Failed to save HTML: {e}")

def test_username_step(driver, wait, debug_dir, username):
    """Test the username step"""
    print("\n=== STEP 1: Username Entry ===")
    
    # Take screenshot of initial page
    take_screenshot(driver, debug_dir, "01_initial_page")
    save_html(driver, debug_dir, "01_initial_page")
    
    # Look for username field
    print("Looking for username field...")
    username_field = None
    
    username_selectors = [
        ("ID", "login-form_username"),
        ("CSS", "#login-form_username input"),
        ("CSS", "sdf-input[id='login-form_username'] input"),
        ("CSS", "input[autocomplete='username']"),
        ("XPATH", "//sdf-input[@id='login-form_username']//input"),
        ("XPATH", "//input[@autocomplete='username']"),
    ]
    
    for selector_type, selector_value in username_selectors:
        try:
            if selector_type == "ID":
                username_field = wait.until(EC.presence_of_element_located((By.ID, selector_value)))
            elif selector_type == "CSS":
                username_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector_value)))
            elif selector_type == "XPATH":
                username_field = wait.until(EC.presence_of_element_located((By.XPATH, selector_value)))
            
            print(f"✅ Found username field: {selector_type}='{selector_value}'")
            break
        except TimeoutException:
            print(f"❌ Not found: {selector_type}='{selector_value}'")
            continue
    
    # If direct input not found, try sdf-input component
    if not username_field:
        try:
            print("Trying sdf-input component...")
            sdf_input = wait.until(EC.presence_of_element_located((By.ID, "login-form_username")))
            
            # Try JavaScript approach
            js_script = f"""
            var sdfInput = document.getElementById('login-form_username');
            if (sdfInput) {{
                sdfInput.value = '{username}';
                sdfInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                sdfInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                return 'success';
            }}
            return 'failed';
            """
            result = driver.execute_script(js_script)
            if result == 'success':
                print("✅ Username set via JavaScript")
                username_field = sdf_input
            else:
                # Fallback to direct interaction
                sdf_input.click()
                time.sleep(1)
                sdf_input.send_keys(username)
                print("✅ Username entered via direct interaction")
                username_field = sdf_input
                
        except Exception as e:
            print(f"❌ Failed to interact with sdf-input: {e}")
            return False
    else:
        # Enter username in found field
        username_field.clear()
        username_field.send_keys(username)
        print("✅ Username entered in input field")
    
    # Take screenshot after entering username
    take_screenshot(driver, debug_dir, "02_username_entered")
    
    # Wait for Next button to be enabled
    time.sleep(2)
    
    # Look for Next button
    print("Looking for Next button...")
    next_button = None
    
    next_selectors = [
        ("ID", "verifUseridBtn"),
        ("CSS", "sdf-button[id='verifUseridBtn']"),
        ("CSS", "button[data-testid='verifUseridBtn']"),
        ("XPATH", "//sdf-button[@id='verifUseridBtn']"),
        ("XPATH", "//button[contains(text(), 'Next')]"),
    ]
    
    for selector_type, selector_value in next_selectors:
        try:
            if selector_type == "ID":
                next_button = driver.find_element(By.ID, selector_value)
            elif selector_type == "CSS":
                next_button = driver.find_element(By.CSS_SELECTOR, selector_value)
            elif selector_type == "XPATH":
                next_button = driver.find_element(By.XPATH, selector_value)
            
            print(f"✅ Found Next button: {selector_type}='{selector_value}'")
            break
        except NoSuchElementException:
            print(f"❌ Not found: {selector_type}='{selector_value}'")
            continue
    
    if not next_button:
        print("❌ Could not find Next button")
        take_screenshot(driver, debug_dir, "03_next_button_not_found")
        return False
    
    # Check if button is enabled
    is_disabled = next_button.get_attribute("disabled")
    if is_disabled:
        print("⚠️ Next button is disabled, waiting...")
        time.sleep(3)
        is_disabled = next_button.get_attribute("disabled")
        if is_disabled:
            print("❌ Next button remains disabled")
            take_screenshot(driver, debug_dir, "04_next_button_disabled")
            return False
    
    # Click Next button
    print("Clicking Next button...")
    try:
        next_button.click()
        print("✅ Next button clicked")
    except Exception as e:
        print(f"⚠️ Regular click failed: {e}, trying JavaScript...")
        try:
            driver.execute_script("arguments[0].click();", next_button)
            print("✅ Next button clicked via JavaScript")
        except Exception as js_error:
            print(f"❌ JavaScript click failed: {js_error}")
            return False
    
    # Take screenshot after clicking Next
    take_screenshot(driver, debug_dir, "05_next_clicked")
    
    # Wait for password page
    time.sleep(5)
    
    print("✅ Username step completed")
    return True

def test_password_step(driver, wait, debug_dir, password):
    """Test the password step"""
    print("\n=== STEP 2: Password Entry ===")
    
    # Take screenshot of password page
    take_screenshot(driver, debug_dir, "06_password_page")
    save_html(driver, debug_dir, "06_password_page")
    
    # Look for password field
    print("Looking for password field...")
    password_field = None
    
    password_selectors = [
        ("CSS", "sdf-input[type='password'] input"),
        ("CSS", "input[type='password']"),
        ("CSS", "input[autocomplete='current-password']"),
        ("ID", "password"),
        ("ID", "login-form_password"),
        ("XPATH", "//input[@type='password']"),
    ]
    
    for selector_type, selector_value in password_selectors:
        try:
            if selector_type == "ID":
                password_field = wait.until(EC.presence_of_element_located((By.ID, selector_value)))
            elif selector_type == "CSS":
                password_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector_value)))
            elif selector_type == "XPATH":
                password_field = wait.until(EC.presence_of_element_located((By.XPATH, selector_value)))
            
            print(f"✅ Found password field: {selector_type}='{selector_value}'")
            break
        except TimeoutException:
            print(f"❌ Not found: {selector_type}='{selector_value}'")
            continue
    
    # If no password field found, look for sdf-input components
    if not password_field:
        try:
            print("Looking for sdf-input components...")
            sdf_inputs = driver.find_elements(By.TAG_NAME, "sdf-input")
            for i, sdf_input in enumerate(sdf_inputs):
                input_type = sdf_input.get_attribute("type")
                print(f"sdf-input {i+1}: type='{input_type}'")
                if input_type == "password":
                    sdf_input.click()
                    time.sleep(1)
                    sdf_input.send_keys(password)
                    password_field = sdf_input
                    print("✅ Password entered via sdf-input")
                    break
            
            if not password_field:
                print("❌ No password field found")
                return False
                
        except Exception as e:
            print(f"❌ Error finding password field: {e}")
            return False
    else:
        # Enter password
        password_field.clear()
        password_field.send_keys(password)
        print("✅ Password entered")
    
    # Take screenshot after entering password
    take_screenshot(driver, debug_dir, "07_password_entered")
    
    # Look for submit button
    print("Looking for submit button...")
    submit_button = None
    
    submit_selectors = [
        ("XPATH", "//sdf-button[contains(text(), 'Sign In')]"),
        ("XPATH", "//button[contains(text(), 'Sign In')]"),
        ("XPATH", "//sdf-button[contains(text(), 'Submit')]"),
        ("CSS", "button[type='submit']"),
        ("ID", "submitBtn"),
    ]
    
    for selector_type, selector_value in submit_selectors:
        try:
            if selector_type == "ID":
                submit_button = driver.find_element(By.ID, selector_value)
            elif selector_type == "CSS":
                submit_button = driver.find_element(By.CSS_SELECTOR, selector_value)
            elif selector_type == "XPATH":
                submit_button = driver.find_element(By.XPATH, selector_value)
            
            print(f"✅ Found submit button: {selector_type}='{selector_value}'")
            break
        except NoSuchElementException:
            print(f"❌ Not found: {selector_type}='{selector_value}'")
            continue
    
    if not submit_button:
        print("❌ Could not find submit button")
        take_screenshot(driver, debug_dir, "08_submit_not_found")
        return False
    
    # Click submit
    print("Clicking submit button...")
    submit_button.click()
    
    # Take screenshot after submit
    take_screenshot(driver, debug_dir, "09_submit_clicked")
    
    # Wait for response
    time.sleep(8)
    
    print("✅ Password step completed")
    return True

def main():
    """Main test function"""
    print("🔍 ADP Two-Step Login Test")
    print("=" * 40)
    
    # Get credentials
    username = input("Enter ADP Username: ").strip()
    if not username:
        print("❌ Username required")
        return
    
    password = input("Enter ADP Password: ").strip()
    if not password:
        print("❌ Password required")
        return
    
    driver, wait, debug_dir = setup_driver()
    
    try:
        # Navigate to ADP
        print("Navigating to ADP...")
        driver.get("https://online.adp.com/olp/olplanding.html?APPID=ACCOUNTANTCONNECT&lightbrand=accountantconnect&lightbrand=accountantconnect")
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        
        # Test username step
        if not test_username_step(driver, wait, debug_dir, username):
            print("❌ Username step failed")
            return
        
        # Test password step
        if not test_password_step(driver, wait, debug_dir, password):
            print("❌ Password step failed")
            return
        
        # Check final result
        final_url = driver.current_url
        print(f"\n🎯 Final URL: {final_url}")
        
        if "runpayroll.adp.com" in final_url or "protected/auth.aspx" in final_url:
            print("✅ Login appears successful!")
        else:
            print("❓ Login result unclear")
        
        take_screenshot(driver, debug_dir, "10_final_result")
        save_html(driver, debug_dir, "10_final_result")
        
        # Keep browser open for inspection
        print("\nBrowser will stay open for 30 seconds...")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        take_screenshot(driver, debug_dir, "error_state")
    
    finally:
        driver.quit()
        print(f"\n📁 Debug files saved to: {debug_dir}")

if __name__ == "__main__":
    main()
