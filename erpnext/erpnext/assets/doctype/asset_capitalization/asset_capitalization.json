{"actions": [], "autoname": "naming_series:", "creation": "2021-09-04 13:38:04.217187", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "naming_series", "target_asset", "target_asset_name", "target_item_code", "finance_book", "target_qty", "column_break_9", "company", "posting_date", "posting_time", "set_posting_time", "target_batch_no", "target_serial_no", "amended_from", "target_is_fixed_asset", "target_has_batch_no", "target_has_serial_no", "section_break_16", "stock_items", "stock_items_total", "section_break_26", "asset_items", "asset_items_total", "service_expenses_section", "service_items", "service_items_total", "totals_section", "total_value", "column_break_36", "target_incoming_rate", "accounting_dimensions_section", "cost_center", "dimension_col_break", "target_fixed_asset_account"], "fields": [{"fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title"}, {"depends_on": "eval:(doc.target_item_code && !doc.__islocal)", "fieldname": "target_item_code", "fieldtype": "Link", "in_standard_filter": 1, "label": "Target Item Code", "options": "<PERSON><PERSON>", "read_only": 1}, {"default": "0", "fetch_from": "target_item_code.is_fixed_asset", "fieldname": "target_is_fixed_asset", "fieldtype": "Check", "hidden": 1, "label": "Target Is Fixed Asset", "read_only": 1}, {"fieldname": "target_asset", "fieldtype": "Link", "in_standard_filter": 1, "label": "Target Asset", "no_copy": 1, "options": "<PERSON><PERSON>"}, {"fetch_from": "target_asset.asset_name", "fieldname": "target_asset_name", "fieldtype": "Data", "label": "Asset Name", "no_copy": 1, "read_only": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Posting Date", "no_copy": 1, "reqd": 1, "search_index": 1}, {"default": "Now", "fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "no_copy": 1, "reqd": 1}, {"default": "0", "depends_on": "eval:doc.docstatus==0", "fieldname": "set_posting_time", "fieldtype": "Check", "label": "Edit Posting Date and Time"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "ACC-ASC-.YYYY.-", "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Asset Capitalization", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.docstatus == 0 || (doc.stock_items && doc.stock_items.length)", "fieldname": "section_break_16", "fieldtype": "Section Break", "label": "Consumed Stock Items"}, {"fieldname": "stock_items", "fieldtype": "Table", "label": "Stock Items", "options": "Asset Capitalization Stock Item"}, {"depends_on": "target_has_batch_no", "fieldname": "target_batch_no", "fieldtype": "Link", "label": "Target Batch No", "options": "<PERSON><PERSON>"}, {"default": "1", "fieldname": "target_qty", "fieldtype": "Float", "hidden": 1, "label": "Target Qty", "read_only": 1}, {"default": "0", "fetch_from": "target_item_code.has_batch_no", "fieldname": "target_has_batch_no", "fieldtype": "Check", "hidden": 1, "label": "Target Has Batch No", "read_only": 1}, {"default": "0", "fetch_from": "target_item_code.has_serial_no", "fieldname": "target_has_serial_no", "fieldtype": "Check", "hidden": 1, "label": "Target Has Serial No", "read_only": 1}, {"depends_on": "target_has_serial_no", "fieldname": "target_serial_no", "fieldtype": "Small Text", "label": "Target Serial No"}, {"depends_on": "eval:doc.docstatus == 0 || (doc.asset_items && doc.asset_items.length)", "fieldname": "section_break_26", "fieldtype": "Section Break", "label": "Consumed Assets"}, {"fieldname": "asset_items", "fieldtype": "Table", "label": "Assets", "options": "Asset Capitalization Asset Item"}, {"fieldname": "stock_items_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Consumed Stock Total Value", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "asset_items_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Consumed Asset Total Value", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "finance_book", "fieldtype": "Link", "label": "Finance Book", "options": "Finance Book"}, {"depends_on": "eval:doc.docstatus == 0 || (doc.service_items && doc.service_items.length)", "fieldname": "service_expenses_section", "fieldtype": "Section Break", "label": "Service Expenses"}, {"fieldname": "service_items", "fieldtype": "Table", "label": "Services", "options": "Asset Capitalization Service Item"}, {"fieldname": "service_items_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Service Expense Total Amount", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "totals_section", "fieldtype": "Section Break", "label": "Totals"}, {"fieldname": "total_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Value", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "column_break_36", "fieldtype": "Column Break"}, {"fieldname": "target_incoming_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Target Incoming Rate", "options": "Company:company:default_currency", "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "target_fixed_asset_account", "fieldtype": "Link", "label": "Target Fixed Asset Account", "options": "Account", "read_only": 1}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-05-20 15:15:12.110035", "modified_by": "Administrator", "module": "Assets", "name": "Asset Capitalization", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Quality Manager", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1, "track_seen": 1}