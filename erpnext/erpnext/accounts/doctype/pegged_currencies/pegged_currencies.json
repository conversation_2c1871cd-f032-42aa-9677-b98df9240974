{"actions": [], "allow_rename": 1, "creation": "2025-05-30 11:47:03.670913", "doctype": "DocType", "engine": "InnoDB", "field_order": ["pegged_currencies_item_section", "pegged_currency_item"], "fields": [{"fieldname": "pegged_currencies_item_section", "fieldtype": "Section Break"}, {"fieldname": "pegged_currency_item", "fieldtype": "Table", "options": "Pegged <PERSON><PERSON><PERSON>cy <PERSON>"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-06-02 11:46:31.936714", "modified_by": "Administrator", "module": "Accounts", "name": "Pegged Currencies", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}