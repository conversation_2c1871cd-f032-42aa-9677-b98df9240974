{"actions": [], "autoname": "POS-CLO-.YYYY.-.#####", "creation": "2018-05-28 19:06:40.830043", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["period_details_section", "period_start_date", "period_end_date", "column_break_3", "posting_date", "posting_time", "pos_opening_entry", "status", "section_break_5", "company", "column_break_7", "pos_profile", "user", "section_break_12", "pos_invoices", "sales_invoices", "taxes_and_charges_section", "taxes", "section_break_13", "column_break_16", "total_quantity", "column_break_ywgl", "net_total", "total_taxes_and_charges", "grand_total", "section_break_11", "payment_reconciliation", "failure_description_section", "error_message", "section_break_14", "amended_from"], "fields": [{"fetch_from": "pos_opening_entry.period_start_date", "fieldname": "period_start_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Period Start Date", "read_only": 1, "reqd": 1}, {"default": "Today", "fieldname": "period_end_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Period End Date", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break", "label": "User Details"}, {"fetch_from": "pos_opening_entry.company", "fetch_if_empty": 1, "fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fetch_from": "pos_opening_entry.pos_profile", "fetch_if_empty": 1, "fieldname": "pos_profile", "fieldtype": "Link", "in_list_view": 1, "label": "POS Profile", "options": "POS Profile", "read_only": 1, "reqd": 1}, {"fetch_from": "pos_opening_entry.user", "fieldname": "user", "fieldtype": "Link", "label": "Cashier", "options": "User", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Modes of Payment"}, {"fieldname": "payment_reconciliation", "fieldtype": "Table", "label": "Payment Reconciliation", "options": "POS Closing Entry Detail"}, {"collapsible_depends_on": "eval:doc.docstatus==0", "fieldname": "section_break_13", "fieldtype": "Section Break", "label": "Totals"}, {"default": "0", "fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "read_only": 1}, {"default": "0", "fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "read_only": 1}, {"fieldname": "total_quantity", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Taxes", "options": "POS Closing Entry Taxes", "read_only": 1}, {"fieldname": "section_break_12", "fieldtype": "Section Break", "label": "Linked Invoices"}, {"fieldname": "section_break_14", "fieldtype": "Section Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "POS Closing Entry", "print_hide": 1, "read_only": 1}, {"fieldname": "pos_opening_entry", "fieldtype": "Link", "label": "POS Opening Entry", "options": "POS Opening Entry", "print_hide": 1, "reqd": 1}, {"allow_on_submit": 1, "default": "Draft", "fieldname": "status", "fieldtype": "Select", "hidden": 1, "label": "Status", "options": "Draft\nSubmitted\nQueued\nFailed\nCancelled", "print_hide": 1, "read_only": 1}, {"fieldname": "period_details_section", "fieldtype": "Section Break", "label": "Period Details"}, {"collapsible": 1, "collapsible_depends_on": "error_message", "depends_on": "error_message", "fieldname": "failure_description_section", "fieldtype": "Section Break", "label": "Failure Description"}, {"depends_on": "error_message", "fieldname": "error_message", "fieldtype": "Small Text", "label": "Error", "read_only": 1}, {"default": "Now", "fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "no_copy": 1, "reqd": 1}, {"fieldname": "pos_invoices", "fieldtype": "Table", "label": "POS Transactions", "options": "POS Invoice Reference", "print_hide": 1, "read_only": 1}, {"fieldname": "sales_invoices", "fieldtype": "Table", "label": "Sales Invoice Transactions", "options": "Sales Invoice Reference", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "taxes_and_charges_section", "fieldtype": "Section Break", "label": "Taxes and Charges"}, {"fieldname": "column_break_ywgl", "fieldtype": "Column Break"}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges", "read_only": 1}], "grid_page_length": 50, "is_submittable": 1, "links": [{"link_doctype": "POS Invoice Merge Log", "link_fieldname": "pos_closing_entry"}], "modified": "2025-06-14 02:38:14.962291", "modified_by": "Administrator", "module": "Accounts", "name": "POS Closing Entry", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}